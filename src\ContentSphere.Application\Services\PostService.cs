using ContentSphere.Abstractions.Repositories;
using ContentSphere.Abstractions.Services;
using ContentSphere.Domain.Entities;
using ContentSphere.Domain.Enums;
using Microsoft.Extensions.Logging;

namespace ContentSphere.Application.Services;

public class PostService : IPostService
{
    private readonly IPostRepository _postRepository;
    private readonly IUserRepository _userRepository;
    private readonly ILogger<PostService> _logger;

    public PostService(
        IPostRepository postRepository,
        IUserRepository userRepository,
        ILogger<PostService> logger)
    {
        _postRepository = postRepository;
        _userRepository = userRepository;
        _logger = logger;
    }

    public async Task<PostResponse> CreatePostAsync(Guid userId, CreatePostRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                throw new ArgumentException("User not found");
            }

            if (!Enum.TryParse<PostType>(request.Type, true, out var postType))
            {
                throw new ArgumentException("Invalid post type");
            }

            var post = new Post
            {
                UserId = userId,
                Type = postType,
                Content = request.Content,
                MediaUrls = request.MediaUrls,
                Hashtags = request.Hashtags,
                MentionedUserIds = request.MentionedUserIds,
                Location = request.Location,
                CommentsEnabled = request.CommentsEnabled,
                LikesVisible = request.LikesVisible,
                PollOptions = request.PollOptions,
                PollExpiresAt = request.PollExpiresAt,
                IsTemporary = request.IsTemporary,
                ExpiresAt = request.ExpiresAt,
                IsPublished = true
            };

            await _postRepository.AddAsync(post, cancellationToken);

            // Update user's post count
            user.PostsCount++;
            await _userRepository.UpdateAsync(user, cancellationToken);

            return await MapToPostResponse(post, user, userId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating post for user: {UserId}", userId);
            throw;
        }
    }

    public async Task<PostResponse?> GetPostAsync(Guid postId, Guid? currentUserId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var post = await _postRepository.GetByIdAsync(postId, cancellationToken);
            if (post == null) return null;

            var user = await _userRepository.GetByIdAsync(post.UserId, cancellationToken);
            if (user == null) return null;

            // Increment view count if not the post owner
            if (currentUserId.HasValue && currentUserId != post.UserId)
            {
                await _postRepository.IncrementViewCountAsync(postId, cancellationToken);
            }

            return await MapToPostResponse(post, user, currentUserId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting post: {PostId}", postId);
            throw;
        }
    }

    public async Task<PostResponse> UpdatePostAsync(Guid postId, Guid userId, UpdatePostRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var post = await _postRepository.GetByIdAsync(postId, cancellationToken);
            if (post == null || post.UserId != userId)
            {
                throw new ArgumentException("Post not found or access denied");
            }

            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                throw new ArgumentException("User not found");
            }

            // Update only provided fields
            if (request.Content != null)
                post.Content = request.Content;
            
            if (request.Hashtags != null)
                post.Hashtags = request.Hashtags;
            
            if (request.CommentsEnabled.HasValue)
                post.CommentsEnabled = request.CommentsEnabled.Value;
            
            if (request.LikesVisible.HasValue)
                post.LikesVisible = request.LikesVisible.Value;

            await _postRepository.UpdateAsync(post, cancellationToken);

            return await MapToPostResponse(post, user, userId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating post: {PostId} for user: {UserId}", postId, userId);
            throw;
        }
    }

    public async Task<bool> DeletePostAsync(Guid postId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var post = await _postRepository.GetByIdAsync(postId, cancellationToken);
            if (post == null || post.UserId != userId)
            {
                return false;
            }

            await _postRepository.DeleteAsync(post, cancellationToken);

            // Update user's post count
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user != null && user.PostsCount > 0)
            {
                user.PostsCount--;
                await _userRepository.UpdateAsync(user, cancellationToken);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting post: {PostId} for user: {UserId}", postId, userId);
            throw;
        }
    }

    public async Task<IEnumerable<PostResponse>> GetUserPostsAsync(Guid userId, Guid? currentUserId = null, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default)
    {
        try
        {
            var posts = await _postRepository.GetUserPostsAsync(userId, pageNumber, pageSize, cancellationToken);
            var responses = new List<PostResponse>();

            foreach (var post in posts)
            {
                responses.Add(await MapToPostResponse(post, post.User, currentUserId, cancellationToken));
            }

            return responses;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user posts for user: {UserId}", userId);
            throw;
        }
    }

    public async Task<IEnumerable<PostResponse>> GetFeedPostsAsync(Guid userId, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default)
    {
        try
        {
            var posts = await _postRepository.GetFeedPostsAsync(userId, pageNumber, pageSize, cancellationToken);
            var responses = new List<PostResponse>();

            foreach (var post in posts)
            {
                responses.Add(await MapToPostResponse(post, post.User, userId, cancellationToken));
            }

            return responses;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feed posts for user: {UserId}", userId);
            throw;
        }
    }

    public async Task<IEnumerable<PostResponse>> GetTrendingPostsAsync(Guid? currentUserId = null, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default)
    {
        try
        {
            var posts = await _postRepository.GetTrendingPostsAsync(pageNumber, pageSize, cancellationToken);
            var responses = new List<PostResponse>();

            foreach (var post in posts)
            {
                responses.Add(await MapToPostResponse(post, post.User, currentUserId, cancellationToken));
            }

            return responses;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting trending posts");
            throw;
        }
    }

    public async Task<IEnumerable<PostResponse>> SearchPostsAsync(string searchTerm, Guid? currentUserId = null, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default)
    {
        try
        {
            var posts = await _postRepository.SearchPostsAsync(searchTerm, pageNumber, pageSize, cancellationToken);
            var responses = new List<PostResponse>();

            foreach (var post in posts)
            {
                responses.Add(await MapToPostResponse(post, post.User, currentUserId, cancellationToken));
            }

            return responses;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching posts with term: {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<bool> LikePostAsync(Guid postId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement like post logic using PostLike repository
            // Check if already liked, create like record, increment count
            await _postRepository.IncrementLikeCountAsync(postId, cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error liking post: {PostId} by user: {UserId}", postId, userId);
            throw;
        }
    }

    public async Task<bool> UnlikePostAsync(Guid postId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement unlike post logic
            await _postRepository.DecrementLikeCountAsync(postId, cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unliking post: {PostId} by user: {UserId}", postId, userId);
            throw;
        }
    }

    public async Task<bool> SharePostAsync(Guid postId, Guid userId, string? message = null, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement share post logic using PostShare repository
            await _postRepository.IncrementShareCountAsync(postId, cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sharing post: {PostId} by user: {UserId}", postId, userId);
            throw;
        }
    }

    public async Task<bool> VotePollAsync(Guid postId, Guid userId, string option, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement poll voting logic
            throw new NotImplementedException("Poll voting functionality not implemented yet");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error voting on poll: {PostId} by user: {UserId}", postId, userId);
            throw;
        }
    }

    public async Task IncrementViewCountAsync(Guid postId, CancellationToken cancellationToken = default)
    {
        try
        {
            await _postRepository.IncrementViewCountAsync(postId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing view count for post: {PostId}", postId);
            throw;
        }
    }

    private async Task<PostResponse> MapToPostResponse(Post post, User user, Guid? currentUserId, CancellationToken cancellationToken)
    {
        // TODO: Check if current user has liked this post
        var isLiked = false;

        if (currentUserId.HasValue)
        {
            // Check if current user has liked this post
        }

        return new PostResponse(
            post.Id,
            post.UserId,
            user.FirstName,
            user.LastName,
            user.Username,
            user.ProfileImageUrl,
            user.IsVerified,
            post.Type,
            post.Content,
            post.MediaUrls,
            post.Hashtags,
            post.MentionedUserIds,
            post.LikesCount,
            post.CommentsCount,
            post.SharesCount,
            post.ViewsCount,
            isLiked,
            post.CommentsEnabled,
            post.LikesVisible,
            post.Location,
            post.PollOptions,
            post.PollResults,
            post.PollExpiresAt,
            post.IsTemporary,
            post.ExpiresAt,
            post.CreatedAt);
    }
}
