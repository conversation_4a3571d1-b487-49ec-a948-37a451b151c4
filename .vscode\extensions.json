{
  "recommendations": [
    // .NET Development
    "ms-dotnettools.csharp",
    "ms-dotnettools.csdevkit",
    "ms-dotnettools.vscode-dotnet-runtime",
    
    // Frontend Development
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    
    // Database
    "ms-mssql.mssql",
    "ckolkman.vscode-postgres",
    
    // Docker
    "ms-azuretools.vscode-docker",
    
    // Git
    "eamodio.gitlens",
    
    // General Productivity
    "ms-vscode.powershell",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    
    // Testing
    "ms-dotnettools.vscode-dotnet-test-explorer",
    
    // API Development
    "humao.rest-client",
    "42crunch.vscode-openapi",
    
    // Markdown
    "yzhang.markdown-all-in-one",
    "davidanson.vscode-markdownlint",
    
    // Theme and Icons
    "pkief.material-icon-theme",
    "github.github-vscode-theme"
  ]
}
