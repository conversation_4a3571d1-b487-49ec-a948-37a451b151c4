using ContentSphere.Domain.Common;

namespace ContentSphere.Domain.Entities;

public enum ScheduleStatus
{
    Pending = 0,
    Published = 1,
    Failed = 2,
    Cancelled = 3
}

public enum RecurrenceType
{
    None = 0,
    Daily = 1,
    Weekly = 2,
    Monthly = 3,
    Custom = 4
}

public class ScheduledPost : BaseEntity
{
    public Guid UserId { get; set; }
    public Guid? PostId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public List<string>? MediaUrls { get; set; }
    public List<string>? Hashtags { get; set; }
    public List<string> Platforms { get; set; } = new List<string>(); // YouTube, Instagram, etc.
    public DateTime ScheduledAt { get; set; }
    public ScheduleStatus Status { get; set; } = ScheduleStatus.Pending;
    public string? FailureReason { get; set; }
    public int RetryCount { get; set; } = 0;
    public int MaxRetries { get; set; } = 3;
    public bool IsRecurring { get; set; } = false;
    public RecurrenceType RecurrenceType { get; set; } = RecurrenceType.None;
    public Dictionary<string, object>? RecurrenceSettings { get; set; }
    public DateTime? NextScheduledAt { get; set; }
    public bool IsConditional { get; set; } = false;
    public Dictionary<string, object>? ConditionalSettings { get; set; } // e.g., "when followers reach 10k"
    public Dictionary<string, object>? PlatformSpecificSettings { get; set; }
    public Dictionary<string, object>? AIGeneratedContent { get; set; } // Titles, descriptions, hashtags
    public string? ThumbnailUrl { get; set; }

    // Navigation Properties
    public User User { get; set; } = null!;
    public Post? Post { get; set; }
}
