{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.API\\ContentSphere.API.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Abstractions\\ContentSphere.Abstractions.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Abstractions\\ContentSphere.Abstractions.csproj", "projectName": "ContentSphere.Abstractions", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Abstractions\\ContentSphere.Abstractions.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Abstractions\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Domain\\ContentSphere.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Domain\\ContentSphere.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.API\\ContentSphere.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.API\\ContentSphere.API.csproj", "projectName": "ContentSphere.API", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.API\\ContentSphere.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Abstractions\\ContentSphere.Abstractions.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Abstractions\\ContentSphere.Abstractions.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Application\\ContentSphere.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Application\\ContentSphere.Application.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Domain\\ContentSphere.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Domain\\ContentSphere.Domain.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Infrastructure\\ContentSphere.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Infrastructure\\ContentSphere.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Cors": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.SignalR": {"target": "Package", "version": "[1.1.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[9.0.0, )"}, "Stripe.net": {"target": "Package", "version": "[46.2.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[7.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Application\\ContentSphere.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Application\\ContentSphere.Application.csproj", "projectName": "ContentSphere.Application", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Application\\ContentSphere.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Abstractions\\ContentSphere.Abstractions.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Abstractions\\ContentSphere.Abstractions.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Domain\\ContentSphere.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Domain\\ContentSphere.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.11.0, )"}, "MediatR": {"target": "Package", "version": "[12.4.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Domain\\ContentSphere.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Domain\\ContentSphere.Domain.csproj", "projectName": "ContentSphere.Domain", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Domain\\ContentSphere.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Infrastructure\\ContentSphere.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Infrastructure\\ContentSphere.Infrastructure.csproj", "projectName": "ContentSphere.Infrastructure", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Infrastructure\\ContentSphere.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Abstractions\\ContentSphere.Abstractions.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Abstractions\\ContentSphere.Abstractions.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Domain\\ContentSphere.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\fd\\src\\ContentSphere.Domain\\ContentSphere.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.0, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.16, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}