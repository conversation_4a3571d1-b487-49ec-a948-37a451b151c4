{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=ContentSphere;Username=postgres;Password=your_password"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "JWT": {"SecretKey": "your-super-secret-key-that-is-at-least-32-characters-long", "Issuer": "ContentSphere", "Audience": "ContentSphere-Users", "ExpiryInMinutes": 60, "RefreshTokenExpiryInDays": 7}, "Stripe": {"PublishableKey": "pk_test_...", "SecretKey": "sk_test_...", "WebhookSecret": "whsec_..."}, "PayPal": {"ClientId": "your-paypal-client-id", "ClientSecret": "your-paypal-client-secret", "Environment": "sandbox"}, "OpenAI": {"ApiKey": "your-openai-api-key", "Model": "gpt-4"}, "Redis": {"ConnectionString": "localhost:6379"}, "Email": {"SmtpHost": "smtp.gmail.com", "SmtpPort": 587, "SmtpUsername": "<EMAIL>", "SmtpPassword": "your-app-password", "FromEmail": "<EMAIL>", "FromName": "ContentSphere"}, "FileStorage": {"Provider": "Local", "LocalPath": "wwwroot/uploads", "MaxFileSize": 10485760, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".mp4", ".mov", ".avi"]}}