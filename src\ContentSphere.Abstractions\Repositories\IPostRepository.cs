using ContentSphere.Domain.Entities;
using ContentSphere.Domain.Enums;

namespace ContentSphere.Abstractions.Repositories;

public interface IPostRepository : IBaseRepository<Post>
{
    Task<IEnumerable<Post>> GetUserPostsAsync(Guid userId, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    Task<IEnumerable<Post>> GetFeedPostsAsync(Guid userId, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    Task<IEnumerable<Post>> GetTrendingPostsAsync(int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    Task<IEnumerable<Post>> GetPostsByHashtagAsync(string hashtag, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    Task<IEnumerable<Post>> GetPostsByTypeAsync(PostType type, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    Task<IEnumerable<Post>> SearchPostsAsync(string searchTerm, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    Task<IEnumerable<Post>> GetScheduledPostsAsync(Guid userId, CancellationToken cancellationToken = default);
    Task IncrementViewCountAsync(Guid postId, CancellationToken cancellationToken = default);
    Task IncrementLikeCountAsync(Guid postId, CancellationToken cancellationToken = default);
    Task DecrementLikeCountAsync(Guid postId, CancellationToken cancellationToken = default);
    Task IncrementCommentCountAsync(Guid postId, CancellationToken cancellationToken = default);
    Task DecrementCommentCountAsync(Guid postId, CancellationToken cancellationToken = default);
    Task IncrementShareCountAsync(Guid postId, CancellationToken cancellationToken = default);
}
