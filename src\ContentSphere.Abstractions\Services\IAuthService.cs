using ContentSphere.Domain.Entities;

namespace ContentSphere.Abstractions.Services;

public record LoginRequest(string EmailOrUsername, string Password, bool RememberMe = false);
public record LoginResponse(string AccessToken, string RefreshToken, User User, DateTime ExpiresAt);
public record RegisterRequest(
    string FirstName, 
    string LastName, 
    string Username, 
    string Email, 
    string Password, 
    DateTime DateOfBirth, 
    string Gender,
    string AccountType,
    bool AcceptTrialTerms = true);
public record RegisterResponse(User User, string AccessToken, string RefreshToken);
public record RefreshTokenRequest(string RefreshToken);
public record ChangePasswordRequest(string CurrentPassword, string NewPassword);
public record ResetPasswordRequest(string Email);
public record ConfirmResetPasswordRequest(string Email, string Token, string NewPassword);

public interface IAuthService
{
    Task<LoginResponse> LoginAsync(LoginRequest request, CancellationToken cancellationToken = default);
    Task<RegisterResponse> RegisterAsync(RegisterRequest request, CancellationToken cancellationToken = default);
    Task<LoginResponse> RefreshTokenAsync(RefreshTokenRequest request, CancellationToken cancellationToken = default);
    Task LogoutAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<bool> ChangePasswordAsync(Guid userId, ChangePasswordRequest request, CancellationToken cancellationToken = default);
    Task<bool> ResetPasswordAsync(ResetPasswordRequest request, CancellationToken cancellationToken = default);
    Task<bool> ConfirmResetPasswordAsync(ConfirmResetPasswordRequest request, CancellationToken cancellationToken = default);
    Task<bool> VerifyEmailAsync(string email, string token, CancellationToken cancellationToken = default);
    Task<bool> SendEmailVerificationAsync(string email, CancellationToken cancellationToken = default);
    Task<bool> EnableTwoFactorAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<bool> DisableTwoFactorAsync(Guid userId, string code, CancellationToken cancellationToken = default);
    Task<bool> VerifyTwoFactorAsync(Guid userId, string code, CancellationToken cancellationToken = default);
    Task<string> GenerateQrCodeAsync(Guid userId, CancellationToken cancellationToken = default);
}
