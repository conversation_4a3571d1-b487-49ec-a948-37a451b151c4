using ContentSphere.Domain.Common;
using ContentSphere.Domain.Enums;

namespace ContentSphere.Domain.Entities;

public class User : BaseEntity
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public string PasswordHash { get; set; } = string.Empty;
    public DateTime DateOfBirth { get; set; }
    public Gender Gender { get; set; }
    public AccountType AccountType { get; set; } = AccountType.Regular;
    public SubscriptionPlan SubscriptionPlan { get; set; } = SubscriptionPlan.Free;
    public DateTime? SubscriptionExpiryDate { get; set; }
    public bool IsTrialActive { get; set; } = true;
    public DateTime? TrialExpiryDate { get; set; }
    public string? ProfileImageUrl { get; set; }
    public string? CoverImageUrl { get; set; }
    public string? Bio { get; set; }
    public string? Location { get; set; }
    public string? Website { get; set; }
    public bool IsVerified { get; set; } = false;
    public VerificationBadge VerificationBadge { get; set; } = VerificationBadge.None;
    public bool IsEmailVerified { get; set; } = false;
    public bool IsPhoneVerified { get; set; } = false;
    public bool IsTwoFactorEnabled { get; set; } = false;
    public string? TwoFactorSecret { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public string? LastLoginIp { get; set; }
    public bool IsOnline { get; set; } = false;
    public DateTime? LastSeenAt { get; set; }
    public string? ReferralCode { get; set; }
    public Guid? ReferredByUserId { get; set; }
    public int FollowersCount { get; set; } = 0;
    public int FollowingCount { get; set; } = 0;
    public int PostsCount { get; set; } = 0;
    public string? CustomTheme { get; set; }
    public bool DarkModeEnabled { get; set; } = false;
    public string? TimeZone { get; set; }
    public string? Language { get; set; } = "en";
    public Dictionary<string, object>? Settings { get; set; }
    public Dictionary<string, object>? PrivacySettings { get; set; }
    public Dictionary<string, object>? NotificationSettings { get; set; }

    // Navigation Properties
    public User? ReferredByUser { get; set; }
    public ICollection<User> ReferredUsers { get; set; } = new List<User>();
    public ICollection<Post> Posts { get; set; } = new List<Post>();
    public ICollection<Story> Stories { get; set; } = new List<Story>();
    public ICollection<Follow> Followers { get; set; } = new List<Follow>();
    public ICollection<Follow> Following { get; set; } = new List<Follow>();
    public ICollection<Friendship> SentFriendRequests { get; set; } = new List<Friendship>();
    public ICollection<Friendship> ReceivedFriendRequests { get; set; } = new List<Friendship>();
    public ICollection<Message> SentMessages { get; set; } = new List<Message>();
    public ICollection<Message> ReceivedMessages { get; set; } = new List<Message>();
    public ICollection<Notification> Notifications { get; set; } = new List<Notification>();
    public ICollection<UserDevice> Devices { get; set; } = new List<UserDevice>();
    public ICollection<Subscription> Subscriptions { get; set; } = new List<Subscription>();
    public ICollection<ContentCreatorMembership> CreatorMemberships { get; set; } = new List<ContentCreatorMembership>();
    public ICollection<ContentCreatorMembership> MemberSubscriptions { get; set; } = new List<ContentCreatorMembership>();
}
