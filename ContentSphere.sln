Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC943}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ContentSphere.Domain", "src\ContentSphere.Domain\ContentSphere.Domain.csproj", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC944}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ContentSphere.Abstractions", "src\ContentSphere.Abstractions\ContentSphere.Abstractions.csproj", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC945}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ContentSphere.Infrastructure", "src\ContentSphere.Infrastructure\ContentSphere.Infrastructure.csproj", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC946}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ContentSphere.Application", "src\ContentSphere.Application\ContentSphere.Application.csproj", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC947}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ContentSphere.API", "src\ContentSphere.API\ContentSphere.API.csproj", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC948}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ContentSphere.Workers", "src\ContentSphere.Workers\ContentSphere.Workers.csproj", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC949}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ContentSphere.Tests", "tests\ContentSphere.Tests\ContentSphere.Tests.csproj", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC950}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC944}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC944}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC944}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC944}.Release|Any CPU.Build.0 = Release|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC945}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC945}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC945}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC945}.Release|Any CPU.Build.0 = Release|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC946}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC946}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC946}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC946}.Release|Any CPU.Build.0 = Release|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC947}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC947}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC947}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC947}.Release|Any CPU.Build.0 = Release|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC948}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC948}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC948}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC948}.Release|Any CPU.Build.0 = Release|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC949}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC949}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC949}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC949}.Release|Any CPU.Build.0 = Release|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC950}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC950}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC950}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC950}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC944} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC945} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC946} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC947} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC948} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC949} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC950} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC943}
	EndGlobalSection
EndGlobal
