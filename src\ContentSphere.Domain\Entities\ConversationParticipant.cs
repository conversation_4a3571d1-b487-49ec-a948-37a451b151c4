using ContentSphere.Domain.Common;

namespace ContentSphere.Domain.Entities;

public enum ParticipantRole
{
    Member = 0,
    Admin = 1,
    Owner = 2
}

public class ConversationParticipant : BaseEntity
{
    public Guid ConversationId { get; set; }
    public Guid UserId { get; set; }
    public ParticipantRole Role { get; set; } = ParticipantRole.Member;
    public DateTime JoinedAt { get; set; } = DateTime.UtcNow;
    public DateTime? LeftAt { get; set; }
    public bool IsActive { get; set; } = true;
    public bool NotificationsEnabled { get; set; } = true;
    public DateTime? LastReadAt { get; set; }

    // Navigation Properties
    public Conversation Conversation { get; set; } = null!;
    public User User { get; set; } = null!;
}
