@echo off
echo ========================================
echo    ContentSphere - Quick Start Script
echo ========================================
echo.

REM Check if Docker is running
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed or not running
    echo Please install Docker Desktop and make sure it's running
    pause
    exit /b 1
)

echo ✅ Docker is available

REM Check if .NET 9 is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET 9 SDK is not installed
    echo Please install .NET 9 SDK from https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo ✅ .NET 9 SDK is available

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed
    echo Please install Node.js 18+ from https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js is available
echo.

echo Choose an option:
echo 1. Start with Docker (Recommended)
echo 2. Start Backend only (.NET)
echo 3. Start Frontend only (Next.js)
echo 4. Start Full Development Environment
echo 5. Build and run tests
echo 6. Reset database
echo 7. Exit
echo.

set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto docker_start
if "%choice%"=="2" goto backend_only
if "%choice%"=="3" goto frontend_only
if "%choice%"=="4" goto full_dev
if "%choice%"=="5" goto build_test
if "%choice%"=="6" goto reset_db
if "%choice%"=="7" goto exit
goto invalid_choice

:docker_start
echo.
echo 🐳 Starting ContentSphere with Docker...
echo This will start PostgreSQL, Redis, API, Workers, and Frontend
echo.
docker-compose up -d
if %errorlevel% equ 0 (
    echo.
    echo ✅ ContentSphere is starting up!
    echo.
    echo 📱 Frontend: http://localhost:3000
    echo 🔧 API: http://localhost:5000
    echo 📊 API Documentation: http://localhost:5000/swagger
    echo.
    echo Use 'docker-compose logs -f' to view logs
    echo Use 'docker-compose down' to stop all services
) else (
    echo ❌ Failed to start with Docker
)
pause
goto end

:backend_only
echo.
echo 🔧 Starting Backend only...
echo.
cd src\ContentSphere.API
echo Starting API server...
start cmd /k "dotnet run"
cd ..\ContentSphere.Workers
echo Starting Workers...
start cmd /k "dotnet run"
cd ..\..
echo.
echo ✅ Backend services started!
echo 🔧 API: http://localhost:5000
echo 📊 API Documentation: http://localhost:5000/swagger
pause
goto end

:frontend_only
echo.
echo 📱 Starting Frontend only...
echo.
cd src\ContentSphere.Frontend
if not exist node_modules (
    echo Installing dependencies...
    npm install
)
echo Starting Next.js development server...
start cmd /k "npm run dev"
cd ..\..
echo.
echo ✅ Frontend started!
echo 📱 Frontend: http://localhost:3000
pause
goto end

:full_dev
echo.
echo 🚀 Starting Full Development Environment...
echo.

REM Start PostgreSQL and Redis with Docker
echo Starting database services...
docker-compose up -d postgres redis

REM Wait a bit for services to start
timeout /t 5 /nobreak >nul

REM Start Backend
echo Starting Backend services...
cd src\ContentSphere.API
start cmd /k "dotnet run"
cd ..\ContentSphere.Workers
start cmd /k "dotnet run"
cd ..\..

REM Start Frontend
echo Starting Frontend...
cd src\ContentSphere.Frontend
if not exist node_modules (
    echo Installing dependencies...
    npm install
)
start cmd /k "npm run dev"
cd ..\..

echo.
echo ✅ Full development environment started!
echo.
echo 📱 Frontend: http://localhost:3000
echo 🔧 API: http://localhost:5000
echo 📊 API Documentation: http://localhost:5000/swagger
echo 🗄️ PostgreSQL: localhost:5432
echo 🔴 Redis: localhost:6379
pause
goto end

:build_test
echo.
echo 🔨 Building and running tests...
echo.
dotnet restore
dotnet build
if %errorlevel% equ 0 (
    echo ✅ Build successful!
    echo Running tests...
    dotnet test
    if %errorlevel% equ 0 (
        echo ✅ All tests passed!
    ) else (
        echo ❌ Some tests failed
    )
) else (
    echo ❌ Build failed
)
pause
goto end

:reset_db
echo.
echo 🗄️ Resetting database...
echo ⚠️  This will delete all data!
set /p confirm="Are you sure? (y/N): "
if /i "%confirm%"=="y" (
    cd src\ContentSphere.API
    dotnet ef database drop --force
    dotnet ef database update
    cd ..\..
    echo ✅ Database reset complete!
) else (
    echo Operation cancelled
)
pause
goto end

:invalid_choice
echo ❌ Invalid choice. Please try again.
pause
goto end

:exit
echo Goodbye! 👋
goto end

:end
echo.
