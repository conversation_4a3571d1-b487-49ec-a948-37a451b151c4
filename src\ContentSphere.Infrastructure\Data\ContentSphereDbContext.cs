using ContentSphere.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace ContentSphere.Infrastructure.Data;

public class ContentSphereDbContext : DbContext
{
    public ContentSphereDbContext(DbContextOptions<ContentSphereDbContext> options) : base(options)
    {
    }

    // Users and Authentication
    public DbSet<User> Users { get; set; }
    public DbSet<UserDevice> UserDevices { get; set; }
    public DbSet<Follow> Follows { get; set; }
    public DbSet<Friendship> Friendships { get; set; }

    // Posts and Content
    public DbSet<Post> Posts { get; set; }
    public DbSet<PostLike> PostLikes { get; set; }
    public DbSet<Comment> Comments { get; set; }
    public DbSet<CommentLike> CommentLikes { get; set; }
    public DbSet<PostShare> PostShares { get; set; }
    public DbSet<Story> Stories { get; set; }
    public DbSet<StoryView> StoryViews { get; set; }
    public DbSet<StoryReaction> StoryReactions { get; set; }

    // Messaging
    public DbSet<Conversation> Conversations { get; set; }
    public DbSet<ConversationParticipant> ConversationParticipants { get; set; }
    public DbSet<Message> Messages { get; set; }
    public DbSet<MessageReaction> MessageReactions { get; set; }

    // Scheduling and Publishing
    public DbSet<ScheduledPost> ScheduledPosts { get; set; }

    // Subscriptions and Payments
    public DbSet<Subscription> Subscriptions { get; set; }
    public DbSet<Payment> Payments { get; set; }
    public DbSet<ContentCreatorMembership> ContentCreatorMemberships { get; set; }
    public DbSet<MembershipPayment> MembershipPayments { get; set; }

    // Notifications
    public DbSet<Notification> Notifications { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure JSON columns for PostgreSQL
        ConfigureJsonColumns(modelBuilder);

        // Configure relationships
        ConfigureUserRelationships(modelBuilder);
        ConfigurePostRelationships(modelBuilder);
        ConfigureMessageRelationships(modelBuilder);
        ConfigureSubscriptionRelationships(modelBuilder);

        // Configure indexes
        ConfigureIndexes(modelBuilder);

        // Configure constraints
        ConfigureConstraints(modelBuilder);
    }

    private void ConfigureJsonColumns(ModelBuilder modelBuilder)
    {
        // User JSON columns
        modelBuilder.Entity<User>()
            .Property(e => e.Settings)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb");

        modelBuilder.Entity<User>()
            .Property(e => e.PrivacySettings)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb");

        modelBuilder.Entity<User>()
            .Property(e => e.NotificationSettings)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb");

        // Post JSON columns
        modelBuilder.Entity<Post>()
            .Property(e => e.MediaUrls)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb");

        modelBuilder.Entity<Post>()
            .Property(e => e.Hashtags)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb");

        modelBuilder.Entity<Post>()
            .Property(e => e.MentionedUserIds)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<Guid>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb");

        modelBuilder.Entity<Post>()
            .Property(e => e.PollOptions)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb");

        modelBuilder.Entity<Post>()
            .Property(e => e.PollResults)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, int>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb");

        modelBuilder.Entity<Post>()
            .Property(e => e.Metadata)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb");
    }

    private void ConfigureUserRelationships(ModelBuilder modelBuilder)
    {
        // User self-referencing relationship for referrals
        modelBuilder.Entity<User>()
            .HasOne(u => u.ReferredByUser)
            .WithMany(u => u.ReferredUsers)
            .HasForeignKey(u => u.ReferredByUserId)
            .OnDelete(DeleteBehavior.SetNull);

        // Follow relationships
        modelBuilder.Entity<Follow>()
            .HasOne(f => f.Follower)
            .WithMany(u => u.Following)
            .HasForeignKey(f => f.FollowerId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<Follow>()
            .HasOne(f => f.Following)
            .WithMany(u => u.Followers)
            .HasForeignKey(f => f.FollowingId)
            .OnDelete(DeleteBehavior.Cascade);

        // Friendship relationships
        modelBuilder.Entity<Friendship>()
            .HasOne(f => f.Requester)
            .WithMany(u => u.SentFriendRequests)
            .HasForeignKey(f => f.RequesterId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<Friendship>()
            .HasOne(f => f.Requested)
            .WithMany(u => u.ReceivedFriendRequests)
            .HasForeignKey(f => f.RequestedId)
            .OnDelete(DeleteBehavior.Cascade);
    }

    private void ConfigurePostRelationships(ModelBuilder modelBuilder)
    {
        // Post relationships
        modelBuilder.Entity<Post>()
            .HasOne(p => p.User)
            .WithMany(u => u.Posts)
            .HasForeignKey(p => p.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // PostLike relationships
        modelBuilder.Entity<PostLike>()
            .HasOne(pl => pl.Post)
            .WithMany(p => p.Likes)
            .HasForeignKey(pl => pl.PostId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<PostLike>()
            .HasOne(pl => pl.User)
            .WithMany()
            .HasForeignKey(pl => pl.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // Comment relationships
        modelBuilder.Entity<Comment>()
            .HasOne(c => c.Post)
            .WithMany(p => p.Comments)
            .HasForeignKey(c => c.PostId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<Comment>()
            .HasOne(c => c.User)
            .WithMany()
            .HasForeignKey(c => c.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<Comment>()
            .HasOne(c => c.ParentComment)
            .WithMany(c => c.Replies)
            .HasForeignKey(c => c.ParentCommentId)
            .OnDelete(DeleteBehavior.Cascade);
    }

    private void ConfigureMessageRelationships(ModelBuilder modelBuilder)
    {
        // Message relationships
        modelBuilder.Entity<Message>()
            .HasOne(m => m.Sender)
            .WithMany(u => u.SentMessages)
            .HasForeignKey(m => m.SenderId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<Message>()
            .HasOne(m => m.Receiver)
            .WithMany(u => u.ReceivedMessages)
            .HasForeignKey(m => m.ReceiverId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<Message>()
            .HasOne(m => m.Conversation)
            .WithMany(c => c.Messages)
            .HasForeignKey(m => m.ConversationId)
            .OnDelete(DeleteBehavior.Cascade);

        // ConversationParticipant relationships
        modelBuilder.Entity<ConversationParticipant>()
            .HasOne(cp => cp.Conversation)
            .WithMany(c => c.Participants)
            .HasForeignKey(cp => cp.ConversationId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<ConversationParticipant>()
            .HasOne(cp => cp.User)
            .WithMany()
            .HasForeignKey(cp => cp.UserId)
            .OnDelete(DeleteBehavior.Cascade);
    }

    private void ConfigureSubscriptionRelationships(ModelBuilder modelBuilder)
    {
        // Subscription relationships
        modelBuilder.Entity<Subscription>()
            .HasOne(s => s.User)
            .WithMany(u => u.Subscriptions)
            .HasForeignKey(s => s.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // ContentCreatorMembership relationships
        modelBuilder.Entity<ContentCreatorMembership>()
            .HasOne(ccm => ccm.Creator)
            .WithMany(u => u.CreatorMemberships)
            .HasForeignKey(ccm => ccm.CreatorId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<ContentCreatorMembership>()
            .HasOne(ccm => ccm.Member)
            .WithMany(u => u.MemberSubscriptions)
            .HasForeignKey(ccm => ccm.MemberId)
            .OnDelete(DeleteBehavior.Cascade);
    }

    private void ConfigureIndexes(ModelBuilder modelBuilder)
    {
        // User indexes
        modelBuilder.Entity<User>()
            .HasIndex(u => u.Email)
            .IsUnique();

        modelBuilder.Entity<User>()
            .HasIndex(u => u.Username)
            .IsUnique();

        modelBuilder.Entity<User>()
            .HasIndex(u => u.ReferralCode)
            .IsUnique();

        // Post indexes
        modelBuilder.Entity<Post>()
            .HasIndex(p => p.UserId);

        modelBuilder.Entity<Post>()
            .HasIndex(p => p.CreatedAt);

        // Follow indexes
        modelBuilder.Entity<Follow>()
            .HasIndex(f => new { f.FollowerId, f.FollowingId })
            .IsUnique();

        // Message indexes
        modelBuilder.Entity<Message>()
            .HasIndex(m => m.ConversationId);

        modelBuilder.Entity<Message>()
            .HasIndex(m => new { m.SenderId, m.ReceiverId });
    }

    private void ConfigureConstraints(ModelBuilder modelBuilder)
    {
        // Prevent self-following
        modelBuilder.Entity<Follow>()
            .HasCheckConstraint("CK_Follow_NoSelfFollow", "\"FollowerId\" != \"FollowingId\"");

        // Prevent self-friendship
        modelBuilder.Entity<Friendship>()
            .HasCheckConstraint("CK_Friendship_NoSelfFriend", "\"RequesterId\" != \"RequestedId\"");
    }
}
