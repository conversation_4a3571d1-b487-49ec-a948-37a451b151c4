using ContentSphere.Domain.Common;
using ContentSphere.Domain.Enums;

namespace ContentSphere.Domain.Entities;

public enum PaymentStatus
{
    Pending = 0,
    Completed = 1,
    Failed = 2,
    Cancelled = 3,
    Refunded = 4
}

public class Payment : BaseEntity
{
    public Guid UserId { get; set; }
    public Guid? SubscriptionId { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "USD";
    public PaymentMethod PaymentMethod { get; set; }
    public PaymentStatus Status { get; set; } = PaymentStatus.Pending;
    public string? TransactionId { get; set; }
    public string? PaymentIntentId { get; set; } // Stripe Payment Intent ID
    public string? FailureReason { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
    public string? Description { get; set; }
    public string? InvoiceUrl { get; set; }

    // Navigation Properties
    public User User { get; set; } = null!;
    public Subscription? Subscription { get; set; }
}

public class MembershipPayment : BaseEntity
{
    public Guid MembershipId { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "USD";
    public PaymentMethod PaymentMethod { get; set; }
    public PaymentStatus Status { get; set; } = PaymentStatus.Pending;
    public string? TransactionId { get; set; }
    public string? FailureReason { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }

    // Navigation Properties
    public ContentCreatorMembership Membership { get; set; } = null!;
}

public class UserDevice : BaseEntity
{
    public Guid UserId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string DeviceName { get; set; } = string.Empty;
    public string DeviceType { get; set; } = string.Empty; // Mobile, Desktop, Tablet
    public string OperatingSystem { get; set; } = string.Empty;
    public string Browser { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public string? Location { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime LastUsedAt { get; set; } = DateTime.UtcNow;
    public bool IsTrusted { get; set; } = false;
    public string? PushNotificationToken { get; set; }

    // Navigation Properties
    public User User { get; set; } = null!;
}
