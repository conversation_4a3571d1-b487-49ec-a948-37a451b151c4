# ContentSphere - منصة إدارة وسائل التواصل الاجتماعي الشاملة

ContentSphere هو تطبيق اجتماعي عالمي متكامل يجمع بين التواصل الاجتماعي، إدارة المحتوى، وجدولة النشر على منصات التواصل الاجتماعي، بالإضافة إلى أدوات تحليل الأداء المدعومة بالذكاء الاصطناعي.

## 🌟 الميزات الرئيسية

### 📱 التواصل الاجتماعي المتكامل
- **منشورات متنوعة**: نص، صور، فيديوهات، استطلاعات، بث مباشر، منشورات صوتية وتفاعلية
- **ستوري مبهرة**: صور وفيديوهات مع فلاتر ذكية، إحصائيات تفصيلية، أبرز القصص
- **محادثات آمنة**: تشفير من طرف إلى طرف، مكالمات صوتية/فيديو، دردشة مؤقتة
- **نظام متابعة وصداقة**: متابعة، طلبات صداقة، قوائم مخصصة، حظر

### 🎯 بروفايل احترافي ومخصص
- **تصميم مرن**: صورة ملف شخصي وغلاف (GIF وفيديوهات قصيرة)
- **تخصيص فريد**: ثيمات مخصصة، خلفيات تفاعلية، قوالب جاهزة
- **أقسام متنوعة**: أبرز القصص، الإنجازات، معرض الأعمال، متجر صغير
- **رابط مخصص**: contentsphere.com/username مع QR Code

### 📅 جدولة ونشر ذكي
- **دعم جميع المنصات**: YouTube، Instagram، Facebook، TikTok، Twitter، LinkedIn وأكثر
- **ميزات متقدمة**: نشر متعدد المنصات، أفضل أوقات النشر بالذكاء الاصطناعي
- **جدولة ذكية**: نشر متكرر، نشر شرطي، جدولة الستوري
- **إدارة المحتوى**: تقويم تفاعلي، مسودات، مكتبة محتوى، مجموعات

### 🤖 الذكاء الاصطناعي
- **توليد المحتوى**: وصف، هاشتاجات، عناوين جذابة باستخدام GPT-4
- **تحسين المحتوى**: صور مصغرة تلقائية، تحسينات الإضاءة والفلاتر
- **تحليل ذكي**: اقتراحات لتحسين الأداء، تنبؤات الاتجاهات

### 📊 تحليلات شاملة
- **إحصائيات تفصيلية**: مشاهدات، إعجابات، تعليقات، مشاركات
- **مقارنة المنافسين**: باستخدام SocialBlade API
- **تقارير متقدمة**: أسبوعية/شهرية، تحليل الجمهور، تنبؤات الأداء

## 🏗️ البنية التقنية

### Backend (.NET 9)
```
src/
├── ContentSphere.Domain/          # الكيانات والنماذج
├── ContentSphere.Abstractions/    # العقود والواجهات
├── ContentSphere.Infrastructure/  # قاعدة البيانات والتنفيذ
├── ContentSphere.Application/     # منطق الأعمال والخدمات
├── ContentSphere.API/            # واجهات برمجة التطبيقات
└── ContentSphere.Workers/        # الخدمات الخلفية
```

### Frontend (Next.js 14)
```
src/ContentSphere.Frontend/
├── src/
│   ├── app/                      # App Router (Next.js 14)
│   ├── components/               # مكونات قابلة لإعادة الاستخدام
│   ├── hooks/                    # React Hooks مخصصة
│   ├── store/                    # إدارة الحالة (Zustand)
│   ├── styles/                   # الثيمات والأنماط
│   └── utils/                    # وظائف مساعدة
```

### قاعدة البيانات
- **PostgreSQL**: قاعدة البيانات الرئيسية
- **Redis**: التخزين المؤقت والجلسات
- **Entity Framework Core**: ORM مع Code-First

## 🚀 التقنيات المستخدمة

### Backend
- **ASP.NET Core 9** - إطار العمل الرئيسي
- **Entity Framework Core** - ORM
- **PostgreSQL** - قاعدة البيانات
- **Redis** - التخزين المؤقت
- **SignalR** - الاتصال المباشر
- **Quartz.NET** - جدولة المهام
- **AutoMapper** - تحويل الكائنات
- **MediatR** - نمط CQRS

### Frontend
- **Next.js 14** - إطار React
- **TypeScript** - لغة البرمجة
- **Chakra UI** - مكتبة واجهة المستخدم
- **Framer Motion** - الرسوم المتحركة
- **GSAP** - تأثيرات بصرية متقدمة
- **React Query** - إدارة البيانات
- **Zustand** - إدارة الحالة
- **next-themes** - إدارة الثيمات

### خدمات خارجية
- **Stripe & PayPal** - المدفوعات الدولية
- **اتصالات كاش، أورنج كاش، فودافون كاش** - المدفوعات المحلية
- **OpenAI GPT-4** - الذكاء الاصطناعي
- **YouTube, Instagram, TikTok APIs** - تكامل المنصات
- **SocialBlade API** - تحليل المنافسين

## 📦 التثبيت والتشغيل

### متطلبات النظام
- .NET 9 SDK
- Node.js 18+
- PostgreSQL 14+
- Redis (اختياري)

### Backend
```bash
# استنساخ المشروع
git clone https://github.com/your-username/contentsphere.git
cd contentsphere

# تثبيت التبعيات وتشغيل قاعدة البيانات
dotnet restore
dotnet ef database update --project src/ContentSphere.Infrastructure --startup-project src/ContentSphere.API

# تشغيل API
cd src/ContentSphere.API
dotnet run

# تشغيل Workers (في terminal منفصل)
cd src/ContentSphere.Workers
dotnet run
```

### Frontend
```bash
# الانتقال لمجلد Frontend
cd src/ContentSphere.Frontend

# تثبيت التبعيات
npm install

# تشغيل التطبيق
npm run dev
```

### إعداد قاعدة البيانات
```bash
# إنشاء Migration جديد
dotnet ef migrations add InitialCreate --project src/ContentSphere.Infrastructure --startup-project src/ContentSphere.API

# تطبيق التغييرات
dotnet ef database update --project src/ContentSphere.Infrastructure --startup-project src/ContentSphere.API
```

## 🔧 الإعدادات

### Backend (appsettings.json)
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=ContentSphere;Username=postgres;Password=your_password",
    "Redis": "localhost:6379"
  },
  "JWT": {
    "SecretKey": "your-secret-key",
    "ExpiryInMinutes": 60
  },
  "OpenAI": {
    "ApiKey": "your-openai-api-key"
  }
}
```

### Frontend (.env.local)
```env
NEXT_PUBLIC_API_URL=http://localhost:5000/api
NEXT_PUBLIC_SIGNALR_URL=http://localhost:5000
```

## 💳 خطط الاشتراك

### 🆓 Free
- تواصل اجتماعي كامل
- دعم منشئي المحتوى
- بدون إطار حول الصورة

### 💎 Premium ($9.99/شهر)
- نشر وجدولة محدودة
- تحليلات بسيطة
- إطار فضي لامع

### ⭐ Plus ($19.99/شهر)
- نشر وجدولة كاملة
- تحليلات متقدمة
- إطار ذهبي متلألئ

### 🚀 Pro ($39.99/شهر)
- كل الميزات
- ذكاء اصطناعي كامل
- إطار ماسي متوهج

## 🎨 الميزات البصرية الفريدة

### تأثيرات Nebula
- **Switch Nebula**: خلفية سديمية عند تبديل الحسابات
- **Nebula Pulse**: نبض ملون عند التحويل للوضع المظلم
- **Approval Pulse**: نبض ملون عند الموافقة على الطلبات

### وضع مظلم تفاعلي
- تغيير تلقائي بناءً على الوقت أو الموقع
- تكيف مع الإضاءة المحيطة
- ثيمات متعددة: Nebula Dark، Midnight Black، Starry Night

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

## 📄 الترخيص

هذا المشروع مرخص تحت [MIT License](LICENSE).

## 📞 التواصل

- **الموقع**: [contentsphere.com](https://contentsphere.com)
- **البريد الإلكتروني**: <EMAIL>
- **تويتر**: [@ContentSphere](https://twitter.com/ContentSphere)

---

<div align="center">
  <strong>صُنع بـ ❤️ لمجتمع منشئي المحتوى العربي</strong>
</div>
