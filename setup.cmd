@echo off
echo ========================================
echo    ContentSphere - Setup Script
echo ========================================
echo.

echo 🚀 Setting up ContentSphere development environment...
echo.

REM Check prerequisites
echo Checking prerequisites...

REM Check .NET 9
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET 9 SDK is not installed
    echo Please install .NET 9 SDK from https://dotnet.microsoft.com/download
    pause
    exit /b 1
)
echo ✅ .NET 9 SDK found

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed
    echo Please install Node.js 18+ from https://nodejs.org
    pause
    exit /b 1
)
echo ✅ Node.js found

REM Check Docker (optional)
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Docker not found (optional for development)
) else (
    echo ✅ Docker found
)

echo.
echo 📦 Installing dependencies...

REM Restore .NET packages
echo Restoring .NET packages...
dotnet restore
if %errorlevel% neq 0 (
    echo ❌ Failed to restore .NET packages
    pause
    exit /b 1
)
echo ✅ .NET packages restored

REM Install Node.js packages for root
echo Installing root Node.js packages...
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install root Node.js packages
    pause
    exit /b 1
)
echo ✅ Root Node.js packages installed

REM Install Frontend packages
echo Installing Frontend packages...
cd src\ContentSphere.Frontend
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install Frontend packages
    pause
    exit /b 1
)
cd ..\..
echo ✅ Frontend packages installed

echo.
echo 🗄️ Setting up database...

REM Check if PostgreSQL is running (Docker)
docker ps | findstr postgres >nul 2>&1
if %errorlevel% neq 0 (
    echo Starting PostgreSQL with Docker...
    docker-compose up -d postgres
    timeout /t 10 /nobreak >nul
)

REM Run database migrations
echo Running database migrations...
cd src\ContentSphere.API
dotnet ef database update
if %errorlevel% neq 0 (
    echo ⚠️  Database migration failed (this is normal if PostgreSQL is not running)
    echo You can run migrations later with: dotnet ef database update
) else (
    echo ✅ Database migrations completed
)
cd ..\..

echo.
echo 🔧 Creating configuration files...

REM Create appsettings.Development.json if it doesn't exist
if not exist "src\ContentSphere.API\appsettings.Development.json" (
    echo Creating appsettings.Development.json...
    copy "src\ContentSphere.API\appsettings.json" "src\ContentSphere.API\appsettings.Development.json" >nul
    echo ✅ Development configuration created
)

REM Create .env.local for Frontend if it doesn't exist
if not exist "src\ContentSphere.Frontend\.env.local" (
    echo Creating Frontend .env.local...
    echo NEXT_PUBLIC_API_URL=http://localhost:5000/api > "src\ContentSphere.Frontend\.env.local"
    echo NEXT_PUBLIC_SIGNALR_URL=http://localhost:5000 >> "src\ContentSphere.Frontend\.env.local"
    echo ✅ Frontend environment file created
)

echo.
echo 🎉 Setup completed successfully!
echo.
echo 📋 Next steps:
echo.
echo 1. Start the development environment:
echo    - Run: run.cmd
echo    - Or use: npm run dev
echo.
echo 2. Access the application:
echo    - Frontend: http://localhost:3000
echo    - API: http://localhost:5000
echo    - API Docs: http://localhost:5000/swagger
echo.
echo 3. For Docker setup:
echo    - Run: docker-compose up -d
echo.
echo 📚 Documentation:
echo    - README.md for detailed instructions
echo    - API documentation at /swagger endpoint
echo.

pause
