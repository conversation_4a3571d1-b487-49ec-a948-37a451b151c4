{"name": "contentsphere", "version": "1.0.0", "description": "ContentSphere - Social Media Management Platform", "scripts": {"dev": "concurrently \"npm run dev:api\" \"npm run dev:workers\" \"npm run dev:frontend\"", "dev:api": "cd src/ContentSphere.API && dotnet run", "dev:workers": "cd src/ContentSphere.Workers && dotnet run", "dev:frontend": "cd src/ContentSphere.Frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "dotnet build", "build:frontend": "cd src/ContentSphere.Frontend && npm run build", "test": "dotnet test", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "db:migrate": "cd src/ContentSphere.API && dotnet ef database update", "db:reset": "cd src/ContentSphere.API && dotnet ef database drop --force && dotnet ef database update", "install:frontend": "cd src/ContentSphere.Frontend && npm install", "lint:frontend": "cd src/ContentSphere.Frontend && npm run lint", "format": "dotnet format"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["social-media", "content-management", "scheduling", "analytics", "ai", "dotnet", "nextjs", "typescript"], "author": "ContentSphere Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/contentsphere.git"}, "bugs": {"url": "https://github.com/your-username/contentsphere/issues"}, "homepage": "https://contentsphere.com"}