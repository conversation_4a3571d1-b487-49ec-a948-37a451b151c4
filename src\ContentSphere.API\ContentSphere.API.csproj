<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.1.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" />
    <PackageReference Include="Stripe.net" Version="46.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Cors" Version="2.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ContentSphere.Domain\ContentSphere.Domain.csproj" />
    <ProjectReference Include="..\ContentSphere.Abstractions\ContentSphere.Abstractions.csproj" />
    <ProjectReference Include="..\ContentSphere.Infrastructure\ContentSphere.Infrastructure.csproj" />
    <ProjectReference Include="..\ContentSphere.Application\ContentSphere.Application.csproj" />
  </ItemGroup>

</Project>
