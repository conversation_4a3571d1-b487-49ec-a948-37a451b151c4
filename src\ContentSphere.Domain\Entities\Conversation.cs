using ContentSphere.Domain.Common;

namespace ContentSphere.Domain.Entities;

public enum ConversationType
{
    Individual = 0,
    Group = 1
}

public class Conversation : BaseEntity
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? ImageUrl { get; set; }
    public ConversationType Type { get; set; } = ConversationType.Individual;
    public Guid? CreatedByUserId { get; set; }
    public bool IsEncrypted { get; set; } = true;
    public DateTime? LastMessageAt { get; set; }
    public string? LastMessageContent { get; set; }
    public bool IsArchived { get; set; } = false;
    public bool IsMuted { get; set; } = false;
    public Dictionary<string, object>? Settings { get; set; }

    // Navigation Properties
    public User? CreatedByUser { get; set; }
    public ICollection<ConversationParticipant> Participants { get; set; } = new List<ConversationParticipant>();
    public ICollection<Message> Messages { get; set; } = new List<Message>();
}
