using ContentSphere.Domain.Entities;

namespace ContentSphere.Abstractions.Services;

public record UpdateProfileRequest(
    string? FirstName = null,
    string? LastName = null,
    string? Bio = null,
    string? Location = null,
    string? Website = null,
    string? ProfileImageUrl = null,
    string? CoverImageUrl = null);

public record UserProfileResponse(
    Guid Id,
    string FirstName,
    string LastName,
    string Username,
    string Email,
    string? Bio,
    string? Location,
    string? Website,
    string? ProfileImageUrl,
    string? CoverImageUrl,
    bool IsVerified,
    string VerificationBadge,
    int FollowersCount,
    int FollowingCount,
    int PostsCount,
    bool IsFollowing,
    bool IsFriend,
    DateTime CreatedAt);

public interface IUserService
{
    Task<UserProfileResponse?> GetUserProfileAsync(Guid userId, Guid? currentUserId = null, CancellationToken cancellationToken = default);
    Task<UserProfileResponse?> GetUserProfileByUsernameAsync(string username, Guid? currentUserId = null, CancellationToken cancellationToken = default);
    Task<UserProfileResponse> UpdateProfileAsync(Guid userId, UpdateProfileRequest request, CancellationToken cancellationToken = default);
    Task<bool> FollowUserAsync(Guid followerId, Guid followingId, CancellationToken cancellationToken = default);
    Task<bool> UnfollowUserAsync(Guid followerId, Guid followingId, CancellationToken cancellationToken = default);
    Task<bool> SendFriendRequestAsync(Guid requesterId, Guid requestedId, string? message = null, CancellationToken cancellationToken = default);
    Task<bool> AcceptFriendRequestAsync(Guid requestedId, Guid requesterId, CancellationToken cancellationToken = default);
    Task<bool> DeclineFriendRequestAsync(Guid requestedId, Guid requesterId, CancellationToken cancellationToken = default);
    Task<bool> BlockUserAsync(Guid blockerId, Guid blockedId, CancellationToken cancellationToken = default);
    Task<bool> UnblockUserAsync(Guid blockerId, Guid blockedId, CancellationToken cancellationToken = default);
    Task<IEnumerable<UserProfileResponse>> SearchUsersAsync(string searchTerm, Guid? currentUserId = null, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    Task<IEnumerable<UserProfileResponse>> GetSuggestedUsersAsync(Guid userId, int count = 10, CancellationToken cancellationToken = default);
    Task<IEnumerable<UserProfileResponse>> GetFollowersAsync(Guid userId, Guid? currentUserId = null, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    Task<IEnumerable<UserProfileResponse>> GetFollowingAsync(Guid userId, Guid? currentUserId = null, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    Task<bool> UpdateOnlineStatusAsync(Guid userId, bool isOnline, CancellationToken cancellationToken = default);
    Task<bool> DeleteAccountAsync(Guid userId, string password, CancellationToken cancellationToken = default);
}
