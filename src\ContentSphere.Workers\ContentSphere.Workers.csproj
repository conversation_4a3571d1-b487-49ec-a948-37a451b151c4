<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-ContentSphere.Workers-20241201-120000</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="9.0.0" />
    <PackageReference Include="Quartz" Version="3.13.1" />
    <PackageReference Include="Quartz.Extensions.Hosting" Version="3.13.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ContentSphere.Domain\ContentSphere.Domain.csproj" />
    <ProjectReference Include="..\ContentSphere.Abstractions\ContentSphere.Abstractions.csproj" />
    <ProjectReference Include="..\ContentSphere.Infrastructure\ContentSphere.Infrastructure.csproj" />
    <ProjectReference Include="..\ContentSphere.Application\ContentSphere.Application.csproj" />
  </ItemGroup>

</Project>
