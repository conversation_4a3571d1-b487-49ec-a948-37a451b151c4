using ContentSphere.Domain.Common;
using ContentSphere.Domain.Enums;

namespace ContentSphere.Domain.Entities;

public class Notification : BaseEntity
{
    public Guid UserId { get; set; }
    public Guid? FromUserId { get; set; }
    public NotificationType Type { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string? ImageUrl { get; set; }
    public string? ActionUrl { get; set; }
    public bool IsRead { get; set; } = false;
    public DateTime? ReadAt { get; set; }
    public bool IsInteractive { get; set; } = false;
    public Dictionary<string, object>? InteractiveData { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public int Priority { get; set; } = 0; // 0 = Normal, 1 = High, 2 = Urgent

    // Navigation Properties
    public User User { get; set; } = null!;
    public User? FromUser { get; set; }
}
