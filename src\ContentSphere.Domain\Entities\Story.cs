using ContentSphere.Domain.Common;
using ContentSphere.Domain.Enums;

namespace ContentSphere.Domain.Entities;

public class Story : BaseEntity
{
    public Guid UserId { get; set; }
    public StoryType Type { get; set; }
    public string MediaUrl { get; set; } = string.Empty;
    public string? Caption { get; set; }
    public int ViewsCount { get; set; } = 0;
    public DateTime ExpiresAt { get; set; } = DateTime.UtcNow.AddHours(24);
    public bool IsHighlighted { get; set; } = false;
    public string? HighlightCategory { get; set; }
    public string? HighlightCoverUrl { get; set; }
    public List<string>? Filters { get; set; }
    public Dictionary<string, object>? InteractiveElements { get; set; } // Polls, sliders, etc.
    public bool IsAnonymousViewingEnabled { get; set; } = false;
    public string? Location { get; set; }
    public List<Guid>? MentionedUserIds { get; set; }
    public List<string>? Hashtags { get; set; }

    // Navigation Properties
    public User User { get; set; } = null!;
    public ICollection<StoryView> Views { get; set; } = new List<StoryView>();
    public ICollection<StoryReaction> Reactions { get; set; } = new List<StoryReaction>();
}
