using ContentSphere.Domain.Entities;

namespace ContentSphere.Abstractions.Repositories;

public interface IScheduledPostRepository : IBaseRepository<ScheduledPost>
{
    Task<IEnumerable<ScheduledPost>> GetUserScheduledPostsAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<ScheduledPost>> GetPendingPostsAsync(DateTime? upToDate = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<ScheduledPost>> GetFailedPostsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<ScheduledPost>> GetRecurringPostsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<ScheduledPost>> GetConditionalPostsAsync(CancellationToken cancellationToken = default);
    Task UpdateStatusAsync(Guid scheduledPostId, ScheduleStatus status, string? failureReason = null, CancellationToken cancellationToken = default);
    Task IncrementRetryCountAsync(Guid scheduledPostId, CancellationToken cancellationToken = default);
    Task UpdateNextScheduledTimeAsync(Guid scheduledPostId, DateTime nextScheduledAt, CancellationToken cancellationToken = default);
}
