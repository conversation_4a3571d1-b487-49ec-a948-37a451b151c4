{"dotnet.defaultSolution": "ContentSphere.sln", "files.exclude": {"**/bin": true, "**/obj": true, "**/.next": true, "**/node_modules": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "search.exclude": {"**/bin": true, "**/obj": true, "**/.next": true, "**/node_modules": true, "**/dist": true, "**/build": true}, "typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.workingDirectories": ["src/ContentSphere.Frontend"], "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "files.associations": {"*.json": "jsonc"}, "csharp.format.enable": true, "omnisharp.enableRoslynAnalyzers": true, "omnisharp.enableEditorConfigSupport": true, "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true, "dotnet.codeLens.enableReferencesCodeLens": true, "dotnet.server.useOmnisharp": false, "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"*.csproj": "*.config, appsettings*.json, bundleconfig.json", "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml, .npmrc, .yarnrc", "tsconfig.json": "tsconfig.*.json", "next.config.js": "next-env.d.ts, .env*, tailwind.config.js, postcss.config.js", "docker-compose.yml": "docker-compose.*.yml, <PERSON>er<PERSON>le*", "README.md": "CHANGELOG.md, CONTRIBUTING.md, LICENSE*"}, "git.ignoreLimitWarning": true, "terminal.integrated.defaultProfile.windows": "Command Prompt", "terminal.integrated.profiles.windows": {"Command Prompt": {"path": "C:\\Windows\\System32\\cmd.exe", "args": []}}}