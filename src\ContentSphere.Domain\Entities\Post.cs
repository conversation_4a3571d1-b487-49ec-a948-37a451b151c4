using ContentSphere.Domain.Common;
using ContentSphere.Domain.Enums;

namespace ContentSphere.Domain.Entities;

public class Post : BaseEntity
{
    public Guid UserId { get; set; }
    public PostType Type { get; set; }
    public string? Content { get; set; }
    public List<string>? MediaUrls { get; set; }
    public List<string>? Hashtags { get; set; }
    public List<Guid>? MentionedUserIds { get; set; }
    public int LikesCount { get; set; } = 0;
    public int CommentsCount { get; set; } = 0;
    public int SharesCount { get; set; } = 0;
    public int ViewsCount { get; set; } = 0;
    public bool IsScheduled { get; set; } = false;
    public DateTime? ScheduledAt { get; set; }
    public bool IsPublished { get; set; } = true;
    public bool IsTemporary { get; set; } = false;
    public DateTime? ExpiresAt { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
    public string? Location { get; set; }
    public bool CommentsEnabled { get; set; } = true;
    public bool LikesVisible { get; set; } = true;
    public List<string>? PollOptions { get; set; }
    public Dictionary<string, int>? PollResults { get; set; }
    public DateTime? PollExpiresAt { get; set; }
    public bool IsHighlighted { get; set; } = false;
    public bool IsPinned { get; set; } = false;

    // Navigation Properties
    public User User { get; set; } = null!;
    public ICollection<PostLike> Likes { get; set; } = new List<PostLike>();
    public ICollection<Comment> Comments { get; set; } = new List<Comment>();
    public ICollection<PostShare> Shares { get; set; } = new List<PostShare>();
    public ICollection<ScheduledPost> ScheduledPosts { get; set; } = new List<ScheduledPost>();
}
