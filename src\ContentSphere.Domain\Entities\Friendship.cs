using ContentSphere.Domain.Common;

namespace ContentSphere.Domain.Entities;

public enum FriendshipStatus
{
    Pending = 0,
    Accepted = 1,
    Declined = 2,
    Blocked = 3
}

public class Friendship : BaseEntity
{
    public Guid RequesterId { get; set; }
    public Guid RequestedId { get; set; }
    public FriendshipStatus Status { get; set; } = FriendshipStatus.Pending;
    public DateTime? AcceptedAt { get; set; }
    public DateTime? DeclinedAt { get; set; }
    public DateTime? BlockedAt { get; set; }
    public string? Message { get; set; }

    // Navigation Properties
    public User Requester { get; set; } = null!;
    public User Requested { get; set; } = null!;
}
