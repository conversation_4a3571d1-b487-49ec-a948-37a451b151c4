using ContentSphere.Application;
using ContentSphere.Infrastructure;
using ContentSphere.Workers.Jobs;
using Quartz;

var builder = Host.CreateApplicationBuilder(args);

// Add Application and Infrastructure layers
builder.Services.AddApplication();
builder.Services.AddInfrastructure(builder.Configuration);

// Add Quartz
builder.Services.AddQuartz(q =>
{
    // Configure Quartz to use Microsoft DI container
    q.UseMicrosoftDependencyInjection();

    // Add jobs
    q.AddJob<PostSchedulerJob>(opts => opts.WithIdentity("PostSchedulerJob"));
    q.AddJob<StoryCleanupJob>(opts => opts.WithIdentity("StoryCleanupJob"));
    q.AddJob<MessageCleanupJob>(opts => opts.WithIdentity("MessageCleanupJob"));

    // Add triggers
    q.AddTrigger(opts => opts
        .ForJob("PostSchedulerJob")
        .WithIdentity("PostSchedulerJob-trigger")
        .WithCronSchedule("0 * * * * ?")); // Every minute

    q.AddTrigger(opts => opts
        .ForJob("StoryCleanupJob")
        .WithIdentity("StoryCleanupJob-trigger")
        .WithCronSchedule("0 0 * * * ?")); // Every hour

    q.AddTrigger(opts => opts
        .ForJob("MessageCleanupJob")
        .WithIdentity("MessageCleanupJob-trigger")
        .WithCronSchedule("0 0 2 * * ?")); // Every day at 2 AM
});

// Add Quartz hosted service
builder.Services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);

var host = builder.Build();
host.Run();
