using ContentSphere.Domain.Common;

namespace ContentSphere.Domain.Entities;

public class PostLike : BaseEntity
{
    public Guid PostId { get; set; }
    public Guid UserId { get; set; }

    // Navigation Properties
    public Post Post { get; set; } = null!;
    public User User { get; set; } = null!;
}

public class Comment : BaseEntity
{
    public Guid PostId { get; set; }
    public Guid UserId { get; set; }
    public Guid? ParentCommentId { get; set; }
    public string Content { get; set; } = string.Empty;
    public int LikesCount { get; set; } = 0;
    public int RepliesCount { get; set; } = 0;
    public bool IsEdited { get; set; } = false;
    public DateTime? EditedAt { get; set; }

    // Navigation Properties
    public Post Post { get; set; } = null!;
    public User User { get; set; } = null!;
    public Comment? ParentComment { get; set; }
    public ICollection<Comment> Replies { get; set; } = new List<Comment>();
    public ICollection<CommentLike> Likes { get; set; } = new List<CommentLike>();
}

public class CommentLike : BaseEntity
{
    public Guid CommentId { get; set; }
    public Guid UserId { get; set; }

    // Navigation Properties
    public Comment Comment { get; set; } = null!;
    public User User { get; set; } = null!;
}

public class PostShare : BaseEntity
{
    public Guid PostId { get; set; }
    public Guid UserId { get; set; }
    public string? Message { get; set; }

    // Navigation Properties
    public Post Post { get; set; } = null!;
    public User User { get; set; } = null!;
}

public class StoryView : BaseEntity
{
    public Guid StoryId { get; set; }
    public Guid UserId { get; set; }
    public DateTime ViewedAt { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    public Story Story { get; set; } = null!;
    public User User { get; set; } = null!;
}

public class StoryReaction : BaseEntity
{
    public Guid StoryId { get; set; }
    public Guid UserId { get; set; }
    public string Emoji { get; set; } = string.Empty;

    // Navigation Properties
    public Story Story { get; set; } = null!;
    public User User { get; set; } = null!;
}

public class MessageReaction : BaseEntity
{
    public Guid MessageId { get; set; }
    public Guid UserId { get; set; }
    public string Emoji { get; set; } = string.Empty;

    // Navigation Properties
    public Message Message { get; set; } = null!;
    public User User { get; set; } = null!;
}
