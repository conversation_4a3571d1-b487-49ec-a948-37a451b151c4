using ContentSphere.Domain.Entities;

namespace ContentSphere.Abstractions.Repositories;

public interface IStoryRepository : IBaseRepository<Story>
{
    Task<IEnumerable<Story>> GetUserStoriesAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Story>> GetActiveStoriesAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Story>> GetFollowingStoriesAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Story>> GetHighlightedStoriesAsync(Guid userId, string? category = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<Story>> GetExpiredStoriesAsync(CancellationToken cancellationToken = default);
    Task IncrementViewCountAsync(Guid storyId, CancellationToken cancellationToken = default);
    Task MarkAsHighlightedAsync(Guid storyId, string category, string? coverUrl = null, CancellationToken cancellationToken = default);
    Task RemoveFromHighlightsAsync(Guid storyId, CancellationToken cancellationToken = default);
    Task DeleteExpiredStoriesAsync(CancellationToken cancellationToken = default);
}
