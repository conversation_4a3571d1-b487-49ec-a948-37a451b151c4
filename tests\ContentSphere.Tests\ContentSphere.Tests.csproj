<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.2" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="xunit" Version="2.9.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="FluentAssertions" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\ContentSphere.Domain\ContentSphere.Domain.csproj" />
    <ProjectReference Include="..\..\src\ContentSphere.Abstractions\ContentSphere.Abstractions.csproj" />
    <ProjectReference Include="..\..\src\ContentSphere.Infrastructure\ContentSphere.Infrastructure.csproj" />
    <ProjectReference Include="..\..\src\ContentSphere.Application\ContentSphere.Application.csproj" />
    <ProjectReference Include="..\..\src\ContentSphere.API\ContentSphere.API.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

</Project>
