{"name": "contentsphere-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@microsoft/signalr": "^8.0.0", "axios": "^1.6.2", "framer-motion": "^10.16.16", "gsap": "^3.12.2", "next": "14.0.4", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-icons": "^4.12.0", "react-query": "^3.39.3", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "typescript": "^5.3.3"}}