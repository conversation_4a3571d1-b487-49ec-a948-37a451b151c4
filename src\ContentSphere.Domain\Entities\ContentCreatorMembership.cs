using ContentSphere.Domain.Common;

namespace ContentSphere.Domain.Entities;

public enum MembershipTier
{
    Bronze = 0,
    Silver = 1,
    Gold = 2,
    Platinum = 3
}

public enum MembershipStatus
{
    Active = 0,
    Cancelled = 1,
    Expired = 2,
    Suspended = 3
}

public class ContentCreatorMembership : BaseEntity
{
    public Guid CreatorId { get; set; }
    public Guid MemberId { get; set; }
    public MembershipTier Tier { get; set; }
    public decimal MonthlyPrice { get; set; }
    public MembershipStatus Status { get; set; } = MembershipStatus.Active;
    public DateTime StartDate { get; set; } = DateTime.UtcNow;
    public DateTime? EndDate { get; set; }
    public DateTime? NextBillingDate { get; set; }
    public bool AutoRenew { get; set; } = true;
    public string? CustomBenefits { get; set; }
    public Dictionary<string, object>? MembershipSettings { get; set; }
    public int TotalMonthsSubscribed { get; set; } = 0;
    public decimal TotalAmountPaid { get; set; } = 0;

    // Navigation Properties
    public User Creator { get; set; } = null!;
    public User Member { get; set; } = null!;
    public ICollection<MembershipPayment> Payments { get; set; } = new List<MembershipPayment>();
}
