using ContentSphere.Abstractions.Repositories;
using Quartz;

namespace ContentSphere.Workers.Jobs;

public class StoryCleanupJob : IJob
{
    private readonly IStoryRepository _storyRepository;
    private readonly ILogger<StoryCleanupJob> _logger;

    public StoryCleanupJob(
        IStoryRepository storyRepository,
        ILogger<StoryCleanupJob> logger)
    {
        _storyRepository = storyRepository;
        _logger = logger;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        try
        {
            _logger.LogInformation("Starting story cleanup job at {Time}", DateTime.UtcNow);

            // Delete expired stories that are not highlighted
            await _storyRepository.DeleteExpiredStoriesAsync();

            _logger.LogInformation("Completed story cleanup job at {Time}", DateTime.UtcNow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in story cleanup job");
        }
    }
}
