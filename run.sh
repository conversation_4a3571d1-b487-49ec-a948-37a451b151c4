#!/bin/bash

echo "========================================"
echo "   ContentSphere - Quick Start Script"
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Docker is running
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed${NC}"
    echo "Please install Docker and make sure it's running"
    exit 1
fi

echo -e "${GREEN}✅ Docker is available${NC}"

# Check if .NET 9 is installed
if ! command -v dotnet &> /dev/null; then
    echo -e "${RED}❌ .NET 9 SDK is not installed${NC}"
    echo "Please install .NET 9 SDK from https://dotnet.microsoft.com/download"
    exit 1
fi

echo -e "${GREEN}✅ .NET 9 SDK is available${NC}"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js is not installed${NC}"
    echo "Please install Node.js 18+ from https://nodejs.org"
    exit 1
fi

echo -e "${GREEN}✅ Node.js is available${NC}"
echo

echo "Choose an option:"
echo "1. Start with Docker (Recommended)"
echo "2. Start Backend only (.NET)"
echo "3. Start Frontend only (Next.js)"
echo "4. Start Full Development Environment"
echo "5. Build and run tests"
echo "6. Reset database"
echo "7. Exit"
echo

read -p "Enter your choice (1-7): " choice

case $choice in
    1)
        echo
        echo -e "${BLUE}🐳 Starting ContentSphere with Docker...${NC}"
        echo "This will start PostgreSQL, Redis, API, Workers, and Frontend"
        echo
        
        docker-compose up -d
        
        if [ $? -eq 0 ]; then
            echo
            echo -e "${GREEN}✅ ContentSphere is starting up!${NC}"
            echo
            echo -e "${BLUE}📱 Frontend: http://localhost:3000${NC}"
            echo -e "${BLUE}🔧 API: http://localhost:5000${NC}"
            echo -e "${BLUE}📊 API Documentation: http://localhost:5000/swagger${NC}"
            echo
            echo "Use 'docker-compose logs -f' to view logs"
            echo "Use 'docker-compose down' to stop all services"
        else
            echo -e "${RED}❌ Failed to start with Docker${NC}"
        fi
        ;;
    2)
        echo
        echo -e "${BLUE}🔧 Starting Backend only...${NC}"
        echo
        
        # Start API
        cd src/ContentSphere.API
        echo "Starting API server..."
        gnome-terminal -- bash -c "dotnet run; exec bash" 2>/dev/null || \
        xterm -e "dotnet run" 2>/dev/null || \
        osascript -e 'tell app "Terminal" to do script "cd '$(pwd)' && dotnet run"' 2>/dev/null || \
        dotnet run &
        
        # Start Workers
        cd ../ContentSphere.Workers
        echo "Starting Workers..."
        gnome-terminal -- bash -c "dotnet run; exec bash" 2>/dev/null || \
        xterm -e "dotnet run" 2>/dev/null || \
        osascript -e 'tell app "Terminal" to do script "cd '$(pwd)' && dotnet run"' 2>/dev/null || \
        dotnet run &
        
        cd ../..
        echo
        echo -e "${GREEN}✅ Backend services started!${NC}"
        echo -e "${BLUE}🔧 API: http://localhost:5000${NC}"
        echo -e "${BLUE}📊 API Documentation: http://localhost:5000/swagger${NC}"
        ;;
    3)
        echo
        echo -e "${BLUE}📱 Starting Frontend only...${NC}"
        echo
        
        cd src/ContentSphere.Frontend
        
        if [ ! -d "node_modules" ]; then
            echo "Installing dependencies..."
            npm install
        fi
        
        echo "Starting Next.js development server..."
        gnome-terminal -- bash -c "npm run dev; exec bash" 2>/dev/null || \
        xterm -e "npm run dev" 2>/dev/null || \
        osascript -e 'tell app "Terminal" to do script "cd '$(pwd)' && npm run dev"' 2>/dev/null || \
        npm run dev &
        
        cd ../..
        echo
        echo -e "${GREEN}✅ Frontend started!${NC}"
        echo -e "${BLUE}📱 Frontend: http://localhost:3000${NC}"
        ;;
    4)
        echo
        echo -e "${BLUE}🚀 Starting Full Development Environment...${NC}"
        echo
        
        # Start PostgreSQL and Redis with Docker
        echo "Starting database services..."
        docker-compose up -d postgres redis
        
        # Wait a bit for services to start
        sleep 5
        
        # Start Backend
        echo "Starting Backend services..."
        cd src/ContentSphere.API
        gnome-terminal -- bash -c "dotnet run; exec bash" 2>/dev/null || \
        xterm -e "dotnet run" 2>/dev/null || \
        osascript -e 'tell app "Terminal" to do script "cd '$(pwd)' && dotnet run"' 2>/dev/null || \
        dotnet run &
        
        cd ../ContentSphere.Workers
        gnome-terminal -- bash -c "dotnet run; exec bash" 2>/dev/null || \
        xterm -e "dotnet run" 2>/dev/null || \
        osascript -e 'tell app "Terminal" to do script "cd '$(pwd)' && dotnet run"' 2>/dev/null || \
        dotnet run &
        
        cd ../..
        
        # Start Frontend
        echo "Starting Frontend..."
        cd src/ContentSphere.Frontend
        
        if [ ! -d "node_modules" ]; then
            echo "Installing dependencies..."
            npm install
        fi
        
        gnome-terminal -- bash -c "npm run dev; exec bash" 2>/dev/null || \
        xterm -e "npm run dev" 2>/dev/null || \
        osascript -e 'tell app "Terminal" to do script "cd '$(pwd)' && npm run dev"' 2>/dev/null || \
        npm run dev &
        
        cd ../..
        
        echo
        echo -e "${GREEN}✅ Full development environment started!${NC}"
        echo
        echo -e "${BLUE}📱 Frontend: http://localhost:3000${NC}"
        echo -e "${BLUE}🔧 API: http://localhost:5000${NC}"
        echo -e "${BLUE}📊 API Documentation: http://localhost:5000/swagger${NC}"
        echo -e "${BLUE}🗄️ PostgreSQL: localhost:5432${NC}"
        echo -e "${BLUE}🔴 Redis: localhost:6379${NC}"
        ;;
    5)
        echo
        echo -e "${BLUE}🔨 Building and running tests...${NC}"
        echo
        
        dotnet restore
        dotnet build
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Build successful!${NC}"
            echo "Running tests..."
            dotnet test
            
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}✅ All tests passed!${NC}"
            else
                echo -e "${RED}❌ Some tests failed${NC}"
            fi
        else
            echo -e "${RED}❌ Build failed${NC}"
        fi
        ;;
    6)
        echo
        echo -e "${YELLOW}🗄️ Resetting database...${NC}"
        echo -e "${RED}⚠️  This will delete all data!${NC}"
        read -p "Are you sure? (y/N): " confirm
        
        if [[ $confirm =~ ^[Yy]$ ]]; then
            cd src/ContentSphere.API
            dotnet ef database drop --force
            dotnet ef database update
            cd ../..
            echo -e "${GREEN}✅ Database reset complete!${NC}"
        else
            echo "Operation cancelled"
        fi
        ;;
    7)
        echo "Goodbye! 👋"
        exit 0
        ;;
    *)
        echo -e "${RED}❌ Invalid choice. Please try again.${NC}"
        ;;
esac

echo
read -p "Press Enter to continue..."
