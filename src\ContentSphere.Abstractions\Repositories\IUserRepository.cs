using ContentSphere.Domain.Entities;

namespace ContentSphere.Abstractions.Repositories;

public interface IUserRepository : IBaseRepository<User>
{
    Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<User?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default);
    Task<User?> GetByPhoneNumberAsync(string phoneNumber, CancellationToken cancellationToken = default);
    Task<User?> GetByReferralCodeAsync(string referralCode, CancellationToken cancellationToken = default);
    Task<bool> IsEmailTakenAsync(string email, CancellationToken cancellationToken = default);
    Task<bool> IsUsernameTakenAsync(string username, CancellationToken cancellationToken = default);
    Task<bool> IsPhoneNumberTakenAsync(string phoneNumber, CancellationToken cancellationToken = default);
    Task<IEnumerable<User>> GetFollowersAsync(Guid userId, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    Task<IEnumerable<User>> GetFollowingAsync(Guid userId, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    Task<IEnumerable<User>> SearchUsersAsync(string searchTerm, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    Task<IEnumerable<User>> GetSuggestedUsersAsync(Guid userId, int count = 10, CancellationToken cancellationToken = default);
    Task UpdateLastSeenAsync(Guid userId, CancellationToken cancellationToken = default);
    Task UpdateOnlineStatusAsync(Guid userId, bool isOnline, CancellationToken cancellationToken = default);
}
