using ContentSphere.Domain.Entities;

namespace ContentSphere.Abstractions.Repositories;

public interface IMessageRepository : IBaseRepository<Message>
{
    Task<IEnumerable<Message>> GetConversationMessagesAsync(Guid conversationId, int pageNumber = 1, int pageSize = 50, CancellationToken cancellationToken = default);
    Task<IEnumerable<Message>> GetUserMessagesAsync(Guid userId, Guid otherUserId, int pageNumber = 1, int pageSize = 50, CancellationToken cancellationToken = default);
    Task<Message?> GetLastMessageAsync(Guid conversationId, CancellationToken cancellationToken = default);
    Task<int> GetUnreadCountAsync(Guid userId, Guid? conversationId = null, CancellationToken cancellationToken = default);
    Task MarkAsReadAsync(Guid messageId, CancellationToken cancellationToken = default);
    Task MarkConversationAsReadAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);
    Task MarkAsDeliveredAsync(Guid messageId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Message>> GetTemporaryMessagesAsync(CancellationToken cancellationToken = default);
    Task DeleteExpiredMessagesAsync(CancellationToken cancellationToken = default);
}
