namespace ContentSphere.Domain.Enums;

public enum AccountType
{
    Regular = 0,
    ContentCreator = 1,
    Business = 2,
    Verified = 3
}

public enum Gender
{
    Male = 0,
    Female = 1,
    Other = 2,
    PreferNotToSay = 3
}

public enum SubscriptionPlan
{
    Free = 0,
    Premium = 1,
    Plus = 2,
    Pro = 3
}

public enum PostType
{
    Text = 0,
    Image = 1,
    Video = 2,
    ImageWithText = 3,
    VideoWithText = 4,
    Poll = 5,
    LiveStream = 6,
    Audio = 7,
    Interactive = 8
}

public enum StoryType
{
    Image = 0,
    Video = 1
}

public enum NotificationType
{
    FriendRequest = 0,
    PostLike = 1,
    PostComment = 2,
    Message = 3,
    Event = 4,
    JoinRequest = 5,
    SystemNotification = 6
}

public enum MessageType
{
    Text = 0,
    Image = 1,
    Video = 2,
    Audio = 3,
    File = 4,
    Location = 5
}

public enum PaymentMethod
{
    Stripe = 0,
    PayPal = 1,
    EtisalatCash = 2,
    OrangeCash = 3,
    VodafoneCash = 4
}

public enum VerificationBadge
{
    None = 0,
    ContentCreator = 1,
    Business = 2,
    Celebrity = 3,
    Government = 4
}
