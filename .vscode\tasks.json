{"version": "2.0.0", "tasks": [{"label": "build-api", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/src/ContentSphere.API/ContentSphere.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "build-workers", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/src/ContentSphere.Workers/ContentSphere.Workers.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "build-all", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/ContentSphere.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/src/ContentSphere.API/ContentSphere.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch-api", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/src/ContentSphere.API/ContentSphere.API.csproj"], "problemMatcher": "$msCompile"}, {"label": "watch-workers", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/src/ContentSphere.Workers/ContentSphere.Workers.csproj"], "problemMatcher": "$msCompile"}, {"label": "test", "command": "dotnet", "type": "process", "args": ["test", "${workspaceFolder}/tests/ContentSphere.Tests/ContentSphere.Tests.csproj"], "problemMatcher": "$msCompile", "group": {"kind": "test", "isDefault": true}}, {"label": "restore", "command": "dotnet", "type": "process", "args": ["restore", "${workspaceFolder}/ContentSphere.sln"], "problemMatcher": "$msCompile"}, {"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/ContentSphere.sln"], "problemMatcher": "$msCompile"}, {"label": "ef-update-database", "command": "dotnet", "type": "process", "args": ["ef", "database", "update"], "options": {"cwd": "${workspaceFolder}/src/ContentSphere.API"}, "problemMatcher": "$msCompile"}, {"label": "ef-add-migration", "command": "dotnet", "type": "process", "args": ["ef", "migrations", "add", "${input:migrationName}", "--project", "../ContentSphere.Infrastructure", "--startup-project", "."], "options": {"cwd": "${workspaceFolder}/src/ContentSphere.API"}, "problemMatcher": "$msCompile"}, {"label": "launch-workers", "command": "dotnet", "type": "process", "args": ["run", "--project", "${workspaceFolder}/src/ContentSphere.Workers/ContentSphere.Workers.csproj"], "isBackground": true, "problemMatcher": "$msCompile"}, {"label": "docker-up", "command": "docker-compose", "type": "shell", "args": ["up", "-d"], "options": {"cwd": "${workspaceFolder}"}}, {"label": "docker-down", "command": "docker-compose", "type": "shell", "args": ["down"], "options": {"cwd": "${workspaceFolder}"}}, {"label": "npm-install-frontend", "command": "npm", "type": "shell", "args": ["install"], "options": {"cwd": "${workspaceFolder}/src/ContentSphere.Frontend"}}, {"label": "npm-dev-frontend", "command": "npm", "type": "shell", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/src/ContentSphere.Frontend"}, "isBackground": true}], "inputs": [{"id": "migrationName", "description": "Enter migration name", "default": "NewMigration", "type": "promptString"}]}