version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: contentsphere-postgres
    environment:
      POSTGRES_DB: ContentSphere
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ContentSphere123!
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - contentsphere-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: contentsphere-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - contentsphere-network

  # ContentSphere API
  api:
    build:
      context: .
      dockerfile: src/ContentSphere.API/Dockerfile
    container_name: contentsphere-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:5000
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=ContentSphere;Username=postgres;Password=ContentSphere123!
      - ConnectionStrings__Redis=redis:6379
    ports:
      - "5000:5000"
    depends_on:
      - postgres
      - redis
    networks:
      - contentsphere-network
    volumes:
      - ./src/ContentSphere.API/wwwroot/uploads:/app/wwwroot/uploads

  # ContentSphere Workers
  workers:
    build:
      context: .
      dockerfile: src/ContentSphere.Workers/Dockerfile
    container_name: contentsphere-workers
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=ContentSphere;Username=postgres;Password=ContentSphere123!
      - ConnectionStrings__Redis=redis:6379
    depends_on:
      - postgres
      - redis
      - api
    networks:
      - contentsphere-network

  # ContentSphere Frontend
  frontend:
    build:
      context: ./src/ContentSphere.Frontend
      dockerfile: Dockerfile
    container_name: contentsphere-frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:5000/api
      - NEXT_PUBLIC_SIGNALR_URL=http://localhost:5000
    ports:
      - "3000:3000"
    depends_on:
      - api
    networks:
      - contentsphere-network

volumes:
  postgres_data:
  redis_data:

networks:
  contentsphere-network:
    driver: bridge
