using ContentSphere.Domain.Common;
using ContentSphere.Domain.Enums;

namespace ContentSphere.Domain.Entities;

public class Message : BaseEntity
{
    public Guid SenderId { get; set; }
    public Guid ReceiverId { get; set; }
    public Guid? ConversationId { get; set; }
    public MessageType Type { get; set; } = MessageType.Text;
    public string? Content { get; set; }
    public string? MediaUrl { get; set; }
    public bool IsRead { get; set; } = false;
    public DateTime? ReadAt { get; set; }
    public bool IsDelivered { get; set; } = false;
    public DateTime? DeliveredAt { get; set; }
    public bool IsTemporary { get; set; } = false;
    public DateTime? ExpiresAt { get; set; }
    public bool IsEdited { get; set; } = false;
    public DateTime? EditedAt { get; set; }
    public Guid? ReplyToMessageId { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }

    // Navigation Properties
    public User Sender { get; set; } = null!;
    public User Receiver { get; set; } = null!;
    public Conversation? Conversation { get; set; }
    public Message? ReplyToMessage { get; set; }
    public ICollection<Message> Replies { get; set; } = new List<Message>();
    public ICollection<MessageReaction> Reactions { get; set; } = new List<MessageReaction>();
}
