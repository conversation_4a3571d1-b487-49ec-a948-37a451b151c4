# Use the official .NET 9 runtime as base image
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app

# Use the official .NET 9 SDK for building
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy project files
COPY ["src/ContentSphere.Workers/ContentSphere.Workers.csproj", "src/ContentSphere.Workers/"]
COPY ["src/ContentSphere.Application/ContentSphere.Application.csproj", "src/ContentSphere.Application/"]
COPY ["src/ContentSphere.Infrastructure/ContentSphere.Infrastructure.csproj", "src/ContentSphere.Infrastructure/"]
COPY ["src/ContentSphere.Domain/ContentSphere.Domain.csproj", "src/ContentSphere.Domain/"]
COPY ["src/ContentSphere.Abstractions/ContentSphere.Abstractions.csproj", "src/ContentSphere.Abstractions/"]

# Restore dependencies
RUN dotnet restore "src/ContentSphere.Workers/ContentSphere.Workers.csproj"

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/src/ContentSphere.Workers"
RUN dotnet build "ContentSphere.Workers.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "ContentSphere.Workers.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

ENTRYPOINT ["dotnet", "ContentSphere.Workers.dll"]
