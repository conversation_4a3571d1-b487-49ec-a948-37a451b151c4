# دليل المساهمة في ContentSphere

نرحب بمساهماتكم في تطوير ContentSphere! هذا الدليل سيساعدكم على البدء.

## 🚀 البدء السريع

### متطلبات النظام
- .NET 9 SDK
- Node.js 18+
- PostgreSQL 14+
- Docker (اختياري)
- Git

### إعداد البيئة التطويرية

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/contentsphere.git
cd contentsphere
```

2. **تشغيل سكريبت الإعداد**
```bash
# Windows
setup.cmd

# Linux/Mac
chmod +x setup.sh
./setup.sh
```

3. **تشغيل التطبيق**
```bash
# Windows
run.cmd

# Linux/Mac
./run.sh
```

## 📋 إرشادات المساهمة

### 🔄 سير العمل (Workflow)

1. **Fork المشروع** إلى حسابك
2. **إنشاء branch جديد** للميزة أو الإصلاح
```bash
git checkout -b feature/amazing-feature
```
3. **تطوير التغييرات** مع اتباع معايير الكود
4. **كتابة الاختبارات** للكود الجديد
5. **تشغيل الاختبارات** للتأكد من عدم كسر شيء
6. **Commit التغييرات** مع رسائل واضحة
7. **Push إلى branch** الخاص بك
8. **إنشاء Pull Request**

### 📝 معايير الكود

#### Backend (.NET)
- استخدم **C# 13** مع أحدث الميزات
- اتبع **Clean Architecture** principles
- استخدم **async/await** للعمليات غير المتزامنة
- اكتب **XML documentation** للـ public APIs
- استخدم **nullable reference types**

```csharp
/// <summary>
/// Creates a new user account
/// </summary>
/// <param name="request">User registration data</param>
/// <param name="cancellationToken">Cancellation token</param>
/// <returns>Created user information</returns>
public async Task<UserResponse> CreateUserAsync(
    CreateUserRequest request, 
    CancellationToken cancellationToken = default)
{
    // Implementation
}
```

#### Frontend (Next.js)
- استخدم **TypeScript** بدلاً من JavaScript
- اتبع **React Hooks** patterns
- استخدم **Chakra UI** للمكونات
- اكتب **JSDoc comments** للوظائف المعقدة
- استخدم **custom hooks** لإعادة الاستخدام

```typescript
/**
 * Custom hook for managing user authentication
 */
export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null)
  
  // Implementation
  
  return { user, login, logout, isLoading }
}
```

### 🧪 الاختبارات

#### Backend Tests
```bash
# تشغيل جميع الاختبارات
dotnet test

# تشغيل اختبارات مشروع معين
dotnet test tests/ContentSphere.Tests

# تشغيل مع تغطية الكود
dotnet test --collect:"XPlat Code Coverage"
```

#### Frontend Tests
```bash
cd src/ContentSphere.Frontend

# تشغيل الاختبارات
npm test

# تشغيل مع المراقبة
npm test -- --watch

# تشغيل تغطية الكود
npm test -- --coverage
```

### 📊 قاعدة البيانات

#### إضافة Migration جديد
```bash
cd src/ContentSphere.API
dotnet ef migrations add YourMigrationName --project ../ContentSphere.Infrastructure
dotnet ef database update
```

#### إعادة تعيين قاعدة البيانات
```bash
dotnet ef database drop --force
dotnet ef database update
```

### 🐳 Docker

#### تشغيل مع Docker
```bash
# تشغيل جميع الخدمات
docker-compose up -d

# تشغيل خدمات قاعدة البيانات فقط
docker-compose up -d postgres redis

# مشاهدة اللوجز
docker-compose logs -f

# إيقاف الخدمات
docker-compose down
```

## 🎯 أنواع المساهمات

### 🐛 إصلاح الأخطاء (Bug Fixes)
- ابحث في [Issues](https://github.com/your-username/contentsphere/issues) عن الأخطاء المفتوحة
- أنشئ issue جديد إذا لم تجد الخطأ مسجلاً
- اربط Pull Request بالـ issue المناسب

### ✨ ميزات جديدة (New Features)
- ناقش الميزة في [Discussions](https://github.com/your-username/contentsphere/discussions) أولاً
- تأكد من توافق الميزة مع رؤية المشروع
- اكتب اختبارات شاملة للميزة الجديدة

### 📚 تحسين التوثيق
- تحديث README.md
- إضافة أو تحسين التعليقات في الكود
- كتابة أدلة استخدام جديدة

### 🎨 تحسينات UI/UX
- اتبع نظام التصميم الموجود
- تأكد من التوافق مع الوضع المظلم
- اختبر على أجهزة مختلفة

## 📋 قائمة مراجعة Pull Request

قبل إرسال Pull Request، تأكد من:

- [ ] الكود يتبع معايير المشروع
- [ ] جميع الاختبارات تمر بنجاح
- [ ] لا توجد تحذيرات في البناء
- [ ] التوثيق محدث (إذا لزم الأمر)
- [ ] رسالة commit واضحة ووصفية
- [ ] تم اختبار التغييرات محلياً
- [ ] لا توجد تعارضات مع main branch

## 🏷️ تسمية Commits

استخدم الصيغة التالية لرسائل commit:

```
type(scope): description

feat(auth): add two-factor authentication
fix(api): resolve user registration bug
docs(readme): update installation instructions
style(ui): improve button hover effects
refactor(services): simplify user service logic
test(auth): add unit tests for login flow
chore(deps): update dependencies
```

### أنواع Commits:
- `feat`: ميزة جديدة
- `fix`: إصلاح خطأ
- `docs`: تحديث التوثيق
- `style`: تغييرات التنسيق/التصميم
- `refactor`: إعادة هيكلة الكود
- `test`: إضافة أو تحديث الاختبارات
- `chore`: مهام صيانة

## 🤝 قواعد السلوك

### كن محترماً
- استخدم لغة مهذبة ومحترمة
- احترم آراء الآخرين
- قدم نقد بناء

### كن مفيداً
- ساعد المطورين الجدد
- شارك المعرفة والخبرات
- اقترح حلول بديلة

### كن صبوراً
- انتظر المراجعة قبل الدمج
- اقبل التعليقات والاقتراحات
- تعلم من الأخطاء

## 🆘 الحصول على المساعدة

إذا كنت تحتاج مساعدة:

1. **اقرأ التوثيق** في README.md
2. **ابحث في Issues** الموجودة
3. **انضم إلى Discussions** لطرح الأسئلة
4. **تواصل مع الفريق** عبر البريد الإلكتروني

## 📞 التواصل

- **GitHub Issues**: للأخطاء والميزات
- **GitHub Discussions**: للأسئلة والنقاشات
- **البريد الإلكتروني**: <EMAIL>
- **Discord**: [رابط الخادم]

## 🎉 شكراً لك!

مساهمتك تساعد في جعل ContentSphere أفضل للجميع. نقدر وقتك وجهدك! 

---

<div align="center">
  <strong>صُنع بـ ❤️ من قبل مجتمع ContentSphere</strong>
</div>
