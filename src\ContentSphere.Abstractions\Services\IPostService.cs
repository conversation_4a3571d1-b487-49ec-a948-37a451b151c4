using ContentSphere.Domain.Entities;
using ContentSphere.Domain.Enums;

namespace ContentSphere.Abstractions.Services;

public record CreatePostRequest(
    PostType Type,
    string? Content = null,
    List<string>? MediaUrls = null,
    List<string>? Hashtags = null,
    List<Guid>? MentionedUserIds = null,
    string? Location = null,
    bool CommentsEnabled = true,
    bool LikesVisible = true,
    List<string>? PollOptions = null,
    DateTime? PollExpiresAt = null,
    bool IsTemporary = false,
    DateTime? ExpiresAt = null);

public record UpdatePostRequest(
    string? Content = null,
    List<string>? Hashtags = null,
    bool? CommentsEnabled = null,
    bool? LikesVisible = null);

public record PostResponse(
    Guid Id,
    Guid UserId,
    string UserFirstName,
    string UserLastName,
    string Username,
    string? UserProfileImageUrl,
    bool UserIsVerified,
    PostType Type,
    string? Content,
    List<string>? MediaUrls,
    List<string>? Hashtags,
    List<Guid>? MentionedUserIds,
    int LikesCount,
    int CommentsCount,
    int SharesCount,
    int ViewsCount,
    bool IsLiked,
    bool CommentsEnabled,
    bool LikesVisible,
    string? Location,
    List<string>? PollOptions,
    Dictionary<string, int>? PollResults,
    DateTime? PollExpiresAt,
    bool IsTemporary,
    DateTime? ExpiresAt,
    DateTime CreatedAt);

public interface IPostService
{
    Task<PostResponse> CreatePostAsync(Guid userId, CreatePostRequest request, CancellationToken cancellationToken = default);
    Task<PostResponse?> GetPostAsync(Guid postId, Guid? currentUserId = null, CancellationToken cancellationToken = default);
    Task<PostResponse> UpdatePostAsync(Guid postId, Guid userId, UpdatePostRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeletePostAsync(Guid postId, Guid userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<PostResponse>> GetUserPostsAsync(Guid userId, Guid? currentUserId = null, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    Task<IEnumerable<PostResponse>> GetFeedPostsAsync(Guid userId, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    Task<IEnumerable<PostResponse>> GetTrendingPostsAsync(Guid? currentUserId = null, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    Task<IEnumerable<PostResponse>> SearchPostsAsync(string searchTerm, Guid? currentUserId = null, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    Task<bool> LikePostAsync(Guid postId, Guid userId, CancellationToken cancellationToken = default);
    Task<bool> UnlikePostAsync(Guid postId, Guid userId, CancellationToken cancellationToken = default);
    Task<bool> SharePostAsync(Guid postId, Guid userId, string? message = null, CancellationToken cancellationToken = default);
    Task<bool> VotePollAsync(Guid postId, Guid userId, string option, CancellationToken cancellationToken = default);
    Task IncrementViewCountAsync(Guid postId, CancellationToken cancellationToken = default);
}
