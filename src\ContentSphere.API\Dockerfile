# Use the official .NET 9 runtime as base image
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 5000

# Use the official .NET 9 SDK for building
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy project files
COPY ["src/ContentSphere.API/ContentSphere.API.csproj", "src/ContentSphere.API/"]
COPY ["src/ContentSphere.Application/ContentSphere.Application.csproj", "src/ContentSphere.Application/"]
COPY ["src/ContentSphere.Infrastructure/ContentSphere.Infrastructure.csproj", "src/ContentSphere.Infrastructure/"]
COPY ["src/ContentSphere.Domain/ContentSphere.Domain.csproj", "src/ContentSphere.Domain/"]
COPY ["src/ContentSphere.Abstractions/ContentSphere.Abstractions.csproj", "src/ContentSphere.Abstractions/"]

# Restore dependencies
RUN dotnet restore "src/ContentSphere.API/ContentSphere.API.csproj"

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/src/ContentSphere.API"
RUN dotnet build "ContentSphere.API.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "ContentSphere.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Create uploads directory
RUN mkdir -p /app/wwwroot/uploads

ENTRYPOINT ["dotnet", "ContentSphere.API.dll"]
