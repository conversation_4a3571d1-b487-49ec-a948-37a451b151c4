using ContentSphere.Domain.Common;
using ContentSphere.Domain.Enums;

namespace ContentSphere.Domain.Entities;

public enum SubscriptionStatus
{
    Active = 0,
    Cancelled = 1,
    Expired = 2,
    Suspended = 3,
    Trial = 4
}

public class Subscription : BaseEntity
{
    public Guid UserId { get; set; }
    public SubscriptionPlan Plan { get; set; }
    public SubscriptionStatus Status { get; set; } = SubscriptionStatus.Trial;
    public decimal Price { get; set; }
    public string Currency { get; set; } = "USD";
    public DateTime StartDate { get; set; } = DateTime.UtcNow;
    public DateTime EndDate { get; set; }
    public DateTime? NextBillingDate { get; set; }
    public bool AutoRenew { get; set; } = true;
    public PaymentMethod PaymentMethod { get; set; }
    public string? PaymentMethodId { get; set; } // Stripe customer ID, etc.
    public bool IsTrialUsed { get; set; } = false;
    public DateTime? TrialEndDate { get; set; }
    public string? CancellationReason { get; set; }
    public DateTime? CancelledAt { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }

    // Navigation Properties
    public User User { get; set; } = null!;
    public ICollection<Payment> Payments { get; set; } = new List<Payment>();
}
