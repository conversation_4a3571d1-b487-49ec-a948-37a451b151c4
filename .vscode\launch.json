{"version": "0.2.0", "configurations": [{"name": "Launch API", "type": "coreclr", "request": "launch", "preLaunchTask": "build-api", "program": "${workspaceFolder}/src/ContentSphere.API/bin/Debug/net9.0/ContentSphere.API.dll", "args": [], "cwd": "${workspaceFolder}/src/ContentSphere.API", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Launch Workers", "type": "coreclr", "request": "launch", "preLaunchTask": "build-workers", "program": "${workspaceFolder}/src/ContentSphere.Workers/bin/Debug/net9.0/ContentSphere.Workers.dll", "args": [], "cwd": "${workspaceFolder}/src/ContentSphere.Workers", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}}, {"name": "Launch API + Workers", "type": "coreclr", "request": "launch", "preLaunchTask": "build-all", "program": "${workspaceFolder}/src/ContentSphere.API/bin/Debug/net9.0/ContentSphere.API.dll", "args": [], "cwd": "${workspaceFolder}/src/ContentSphere.API", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "postDebugTask": "launch-workers"}, {"name": "Attach to .NET Process", "type": "coreclr", "request": "attach"}], "compounds": [{"name": "Launch Full Stack", "configurations": ["Launch API", "Launch Workers"], "stopAll": true}]}