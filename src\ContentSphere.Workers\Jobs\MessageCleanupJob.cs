using ContentSphere.Abstractions.Repositories;
using Quartz;

namespace ContentSphere.Workers.Jobs;

public class MessageCleanupJob : IJob
{
    private readonly IMessageRepository _messageRepository;
    private readonly ILogger<MessageCleanupJob> _logger;

    public MessageCleanupJob(
        IMessageRepository messageRepository,
        ILogger<MessageCleanupJob> logger)
    {
        _messageRepository = messageRepository;
        _logger = logger;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        try
        {
            _logger.LogInformation("Starting message cleanup job at {Time}", DateTime.UtcNow);

            // Delete expired temporary messages
            await _messageRepository.DeleteExpiredMessagesAsync();

            _logger.LogInformation("Completed message cleanup job at {Time}", DateTime.UtcNow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in message cleanup job");
        }
    }
}
