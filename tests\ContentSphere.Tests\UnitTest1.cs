using ContentSphere.Domain.Entities;
using ContentSphere.Domain.Enums;
using FluentAssertions;

namespace ContentSphere.Tests;

public class UserTests
{
    [Fact]
    public void User_Creation_Should_Set_Default_Values()
    {
        // Arrange & Act
        var user = new User
        {
            FirstName = "John",
            LastName = "Doe",
            Username = "johndoe",
            Email = "<EMAIL>",
            PasswordHash = "hashedpassword",
            DateOfBirth = new DateTime(1990, 1, 1),
            Gender = Gender.Male,
            AccountType = AccountType.Regular
        };

        // Assert
        user.Id.Should().NotBeEmpty();
        user.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        user.IsDeleted.Should().BeFalse();
        user.SubscriptionPlan.Should().Be(SubscriptionPlan.Free);
        user.IsVerified.Should().BeFalse();
        user.FollowersCount.Should().Be(0);
        user.FollowingCount.Should().Be(0);
        user.PostsCount.Should().Be(0);
    }

    [Fact]
    public void User_Should_Generate_Unique_Ids()
    {
        // Arrange & Act
        var user1 = new User();
        var user2 = new User();

        // Assert
        user1.Id.Should().NotBe(user2.Id);
    }
}

public class PostTests
{
    [Fact]
    public void Post_Creation_Should_Set_Default_Values()
    {
        // Arrange & Act
        var post = new Post
        {
            UserId = Guid.NewGuid(),
            Type = PostType.Text,
            Content = "Test post content"
        };

        // Assert
        post.Id.Should().NotBeEmpty();
        post.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        post.IsDeleted.Should().BeFalse();
        post.LikesCount.Should().Be(0);
        post.CommentsCount.Should().Be(0);
        post.SharesCount.Should().Be(0);
        post.ViewsCount.Should().Be(0);
        post.IsPublished.Should().BeTrue();
        post.CommentsEnabled.Should().BeTrue();
        post.LikesVisible.Should().BeTrue();
    }

    [Theory]
    [InlineData(PostType.Text)]
    [InlineData(PostType.Image)]
    [InlineData(PostType.Video)]
    [InlineData(PostType.Poll)]
    public void Post_Should_Support_Different_Types(PostType postType)
    {
        // Arrange & Act
        var post = new Post
        {
            UserId = Guid.NewGuid(),
            Type = postType,
            Content = "Test content"
        };

        // Assert
        post.Type.Should().Be(postType);
    }
}

public class StoryTests
{
    [Fact]
    public void Story_Should_Expire_After_24_Hours()
    {
        // Arrange & Act
        var story = new Story
        {
            UserId = Guid.NewGuid(),
            Type = StoryType.Image,
            MediaUrl = "https://example.com/image.jpg"
        };

        // Assert
        story.ExpiresAt.Should().BeCloseTo(DateTime.UtcNow.AddHours(24), TimeSpan.FromMinutes(1));
    }

    [Fact]
    public void Story_Should_Not_Be_Highlighted_By_Default()
    {
        // Arrange & Act
        var story = new Story
        {
            UserId = Guid.NewGuid(),
            Type = StoryType.Image,
            MediaUrl = "https://example.com/image.jpg"
        };

        // Assert
        story.IsHighlighted.Should().BeFalse();
        story.HighlightCategory.Should().BeNull();
    }
}
