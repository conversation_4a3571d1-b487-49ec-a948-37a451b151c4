import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { Providers } from './providers'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'ContentSphere - Social Media Management Platform',
  description: 'The ultimate social media management platform with AI-powered content creation, scheduling, and analytics.',
  keywords: 'social media, content management, scheduling, AI, analytics, Instagram, YouTube, TikTok',
  authors: [{ name: 'ContentSphere Team' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#000000' }
  ],
  openGraph: {
    title: 'ContentSphere - Social Media Management Platform',
    description: 'The ultimate social media management platform with AI-powered content creation, scheduling, and analytics.',
    url: 'https://contentsphere.com',
    siteName: 'ContentSphere',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'ContentSphere - Social Media Management Platform'
      }
    ],
    locale: 'en_US',
    type: 'website'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'ContentSphere - Social Media Management Platform',
    description: 'The ultimate social media management platform with AI-powered content creation, scheduling, and analytics.',
    images: ['/og-image.png']
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1
    }
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  )
}
