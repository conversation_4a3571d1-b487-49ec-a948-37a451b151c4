using ContentSphere.Abstractions.Repositories;
using Quartz;

namespace ContentSphere.Workers.Jobs;

public class PostSchedulerJob : IJob
{
    private readonly IScheduledPostRepository _scheduledPostRepository;
    private readonly ILogger<PostSchedulerJob> _logger;

    public PostSchedulerJob(
        IScheduledPostRepository scheduledPostRepository,
        ILogger<PostSchedulerJob> logger)
    {
        _scheduledPostRepository = scheduledPostRepository;
        _logger = logger;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        try
        {
            _logger.LogInformation("Starting post scheduler job at {Time}", DateTime.UtcNow);

            // Get pending posts that should be published now
            var pendingPosts = await _scheduledPostRepository.GetPendingPostsAsync(DateTime.UtcNow);

            foreach (var scheduledPost in pendingPosts)
            {
                try
                {
                    _logger.LogInformation("Processing scheduled post {PostId} for user {UserId}", 
                        scheduledPost.Id, scheduledPost.UserId);

                    // TODO: Implement actual posting logic to social media platforms
                    // For now, just mark as published
                    await _scheduledPostRepository.UpdateStatusAsync(
                        scheduledPost.Id, 
                        Domain.Entities.ScheduleStatus.Published);

                    _logger.LogInformation("Successfully published scheduled post {PostId}", scheduledPost.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to publish scheduled post {PostId}", scheduledPost.Id);
                    
                    // Increment retry count and mark as failed if max retries reached
                    await _scheduledPostRepository.IncrementRetryCountAsync(scheduledPost.Id);
                    
                    if (scheduledPost.RetryCount >= scheduledPost.MaxRetries)
                    {
                        await _scheduledPostRepository.UpdateStatusAsync(
                            scheduledPost.Id, 
                            Domain.Entities.ScheduleStatus.Failed, 
                            ex.Message);
                    }
                }
            }

            // Handle recurring posts
            var recurringPosts = await _scheduledPostRepository.GetRecurringPostsAsync();
            foreach (var recurringPost in recurringPosts)
            {
                if (recurringPost.NextScheduledAt <= DateTime.UtcNow)
                {
                    try
                    {
                        // TODO: Create new scheduled post based on recurrence settings
                        // Calculate next scheduled time based on recurrence type
                        var nextScheduledTime = CalculateNextScheduledTime(recurringPost);
                        await _scheduledPostRepository.UpdateNextScheduledTimeAsync(
                            recurringPost.Id, 
                            nextScheduledTime);

                        _logger.LogInformation("Updated next scheduled time for recurring post {PostId} to {NextTime}", 
                            recurringPost.Id, nextScheduledTime);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to process recurring post {PostId}", recurringPost.Id);
                    }
                }
            }

            _logger.LogInformation("Completed post scheduler job at {Time}", DateTime.UtcNow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in post scheduler job");
        }
    }

    private DateTime CalculateNextScheduledTime(Domain.Entities.ScheduledPost scheduledPost)
    {
        var currentTime = scheduledPost.NextScheduledAt ?? DateTime.UtcNow;

        return scheduledPost.RecurrenceType switch
        {
            Domain.Entities.RecurrenceType.Daily => currentTime.AddDays(1),
            Domain.Entities.RecurrenceType.Weekly => currentTime.AddDays(7),
            Domain.Entities.RecurrenceType.Monthly => currentTime.AddMonths(1),
            Domain.Entities.RecurrenceType.Custom => CalculateCustomRecurrence(scheduledPost, currentTime),
            _ => currentTime.AddDays(1)
        };
    }

    private DateTime CalculateCustomRecurrence(Domain.Entities.ScheduledPost scheduledPost, DateTime currentTime)
    {
        // TODO: Implement custom recurrence logic based on RecurrenceSettings
        // For now, default to daily
        return currentTime.AddDays(1);
    }
}
