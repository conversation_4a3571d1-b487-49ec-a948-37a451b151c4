import { extendTheme, type ThemeConfig } from '@chakra-ui/react'

const config: ThemeConfig = {
  initialColorMode: 'system',
  useSystemColorMode: true,
}

const colors = {
  brand: {
    50: '#f0f0ff',
    100: '#e0e0ff',
    200: '#c7c7ff',
    300: '#a4a4ff',
    400: '#7877c6',
    500: '#6366f1',
    600: '#5855d6',
    700: '#4c46b6',
    800: '#3d3a94',
    900: '#2d2a72',
  },
  nebula: {
    50: '#f8f7ff',
    100: '#f0efff',
    200: '#e1dfff',
    300: '#c9c5ff',
    400: '#a8a1ff',
    500: '#7877c6',
    600: '#6b5eae',
    700: '#5a4d96',
    800: '#483d7e',
    900: '#362d66',
  },
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  }
}

const fonts = {
  heading: 'Inter, sans-serif',
  body: 'Inter, sans-serif',
}

const styles = {
  global: (props: any) => ({
    body: {
      bg: props.colorMode === 'dark' ? 'gray.900' : 'white',
      color: props.colorMode === 'dark' ? 'white' : 'gray.900',
    },
  }),
}

const components = {
  Button: {
    baseStyle: {
      fontWeight: 'semibold',
      borderRadius: 'lg',
    },
    variants: {
      solid: (props: any) => ({
        bg: props.colorScheme === 'brand' ? 'brand.500' : undefined,
        color: 'white',
        _hover: {
          bg: props.colorScheme === 'brand' ? 'brand.600' : undefined,
          transform: 'translateY(-1px)',
        },
        _active: {
          transform: 'translateY(0)',
        },
      }),
      ghost: {
        _hover: {
          transform: 'translateY(-1px)',
        },
        _active: {
          transform: 'translateY(0)',
        },
      },
      outline: {
        _hover: {
          transform: 'translateY(-1px)',
        },
        _active: {
          transform: 'translateY(0)',
        },
      },
    },
  },
  Card: {
    baseStyle: (props: any) => ({
      container: {
        bg: props.colorMode === 'dark' ? 'gray.800' : 'white',
        borderRadius: 'xl',
        border: '1px solid',
        borderColor: props.colorMode === 'dark' ? 'gray.700' : 'gray.200',
        boxShadow: props.colorMode === 'dark' ? 'dark-lg' : 'lg',
        transition: 'all 0.2s',
        _hover: {
          transform: 'translateY(-2px)',
          boxShadow: props.colorMode === 'dark' ? 'dark-xl' : 'xl',
        },
      },
    }),
  },
  Input: {
    variants: {
      filled: (props: any) => ({
        field: {
          bg: props.colorMode === 'dark' ? 'gray.700' : 'gray.50',
          borderRadius: 'lg',
          _hover: {
            bg: props.colorMode === 'dark' ? 'gray.600' : 'gray.100',
          },
          _focus: {
            bg: props.colorMode === 'dark' ? 'gray.600' : 'gray.100',
            borderColor: 'brand.500',
          },
        },
      }),
    },
  },
  Textarea: {
    variants: {
      filled: (props: any) => ({
        bg: props.colorMode === 'dark' ? 'gray.700' : 'gray.50',
        borderRadius: 'lg',
        _hover: {
          bg: props.colorMode === 'dark' ? 'gray.600' : 'gray.100',
        },
        _focus: {
          bg: props.colorMode === 'dark' ? 'gray.600' : 'gray.100',
          borderColor: 'brand.500',
        },
      }),
    },
  },
  Modal: {
    baseStyle: (props: any) => ({
      dialog: {
        bg: props.colorMode === 'dark' ? 'gray.800' : 'white',
        borderRadius: 'xl',
      },
    }),
  },
  Popover: {
    baseStyle: (props: any) => ({
      content: {
        bg: props.colorMode === 'dark' ? 'gray.800' : 'white',
        borderColor: props.colorMode === 'dark' ? 'gray.700' : 'gray.200',
        borderRadius: 'lg',
        boxShadow: props.colorMode === 'dark' ? 'dark-lg' : 'lg',
      },
    }),
  },
  Menu: {
    baseStyle: (props: any) => ({
      list: {
        bg: props.colorMode === 'dark' ? 'gray.800' : 'white',
        borderColor: props.colorMode === 'dark' ? 'gray.700' : 'gray.200',
        borderRadius: 'lg',
        boxShadow: props.colorMode === 'dark' ? 'dark-lg' : 'lg',
      },
    }),
  },
}

export const theme = extendTheme({
  config,
  colors,
  fonts,
  styles,
  components,
})
