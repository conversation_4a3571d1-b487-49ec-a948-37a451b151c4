'use client'

import {
  Box,
  Container,
  Heading,
  Text,
  But<PERSON>,
  VStack,
  HStack,
  SimpleGrid,
  Icon,
  useColorModeValue,
  Flex,
  Badge,
  Card,
  CardBody,
} from '@chakra-ui/react'
import { 
  FiUsers, 
  FiCalendar, 
  FiBarChart3, 
  FiZap, 
  FiStar,
  FiArrowRight,
  FiPlay
} from 'react-icons/fi'
import { motion } from 'framer-motion'
import Link from 'next/link'

const MotionBox = motion(Box)
const MotionCard = motion(Card)

const features = [
  {
    icon: FiUsers,
    title: 'التواصل الاجتماعي المتكامل',
    description: 'منشورات متنوعة، ستوري مبهرة، محادثات آمنة مع تشفير من طرف إلى طرف',
    color: 'blue.500'
  },
  {
    icon: FiCalendar,
    title: 'جدولة ونشر ذكي',
    description: 'نشر متعدد المنصات، أفضل أوقات النشر بالذكاء الاصطناعي، نشر متكرر وشرطي',
    color: 'green.500'
  },
  {
    icon: FiBarChart3,
    title: 'تحليلات شاملة',
    description: 'إحصائيات تفصيلية، مقارنة المنافسين، تقارير أسبوعية وشهرية',
    color: 'purple.500'
  },
  {
    icon: FiZap,
    title: 'الذكاء الاصطناعي',
    description: 'توليد المحتوى، عناوين جذابة، هاشتاجات، صور مصغرة تلقائية',
    color: 'orange.500'
  }
]

const plans = [
  {
    name: 'Free',
    price: 'مجاني',
    description: 'للمستخدمين الجدد',
    features: ['تواصل اجتماعي', 'دعم منشئي المحتوى', 'بدون إطار حول الصورة'],
    color: 'gray',
    popular: false
  },
  {
    name: 'Premium',
    price: '$9.99/شهر',
    description: 'للمستخدمين النشطين',
    features: ['نشر محدود', 'تحليلات بسيطة', 'إطار فضي لامع'],
    color: 'blue',
    popular: false
  },
  {
    name: 'Plus',
    price: '$19.99/شهر',
    description: 'للمحترفين',
    features: ['نشر وجدولة', 'تحليلات متقدمة', 'إطار ذهبي متلألئ'],
    color: 'yellow',
    popular: true
  },
  {
    name: 'Pro',
    price: '$39.99/شهر',
    description: 'للشركات',
    features: ['كل الميزات', 'ذكاء اصطناعي', 'إطار ماسي متوهج'],
    color: 'purple',
    popular: false
  }
]

export default function HomePage() {
  const bgGradient = useColorModeValue(
    'linear(to-br, blue.50, purple.50, pink.50)',
    'linear(to-br, gray.900, blue.900, purple.900)'
  )

  return (
    <Box minH="100vh" bg={bgGradient}>
      {/* Hero Section */}
      <Container maxW="7xl" pt={20} pb={16}>
        <VStack spacing={8} textAlign="center">
          <MotionBox
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Badge
              colorScheme="purple"
              fontSize="sm"
              px={3}
              py={1}
              borderRadius="full"
              mb={4}
            >
              🚀 الآن متاح - فترة تجريبية 7 أيام مجاناً
            </Badge>
            <Heading
              size="3xl"
              bgGradient="linear(to-r, blue.400, purple.500, pink.400)"
              bgClip="text"
              mb={4}
            >
              ContentSphere
            </Heading>
            <Text fontSize="xl" color="gray.600" maxW="2xl" mx="auto">
              منصة إدارة وسائل التواصل الاجتماعي الشاملة مع الذكاء الاصطناعي
              لإنشاء المحتوى والجدولة والتحليلات
            </Text>
          </MotionBox>

          <MotionBox
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <HStack spacing={4}>
              <Button
                as={Link}
                href="/auth/register"
                size="lg"
                colorScheme="purple"
                rightIcon={<Icon as={FiArrowRight} />}
                _hover={{ transform: 'translateY(-2px)' }}
              >
                ابدأ مجاناً
              </Button>
              <Button
                as={Link}
                href="/demo"
                size="lg"
                variant="outline"
                leftIcon={<Icon as={FiPlay} />}
                _hover={{ transform: 'translateY(-2px)' }}
              >
                شاهد العرض التوضيحي
              </Button>
            </HStack>
          </MotionBox>
        </VStack>
      </Container>

      {/* Features Section */}
      <Container maxW="7xl" py={16}>
        <VStack spacing={12}>
          <MotionBox
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            textAlign="center"
          >
            <Heading size="xl" mb={4}>
              ميزات استثنائية لنجاحك
            </Heading>
            <Text fontSize="lg" color="gray.600" maxW="2xl" mx="auto">
              كل ما تحتاجه لإدارة وتنمية حضورك على وسائل التواصل الاجتماعي
            </Text>
          </MotionBox>

          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={8} w="full">
            {features.map((feature, index) => (
              <MotionCard
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                cursor="pointer"
              >
                <CardBody textAlign="center" p={6}>
                  <Icon
                    as={feature.icon}
                    w={12}
                    h={12}
                    color={feature.color}
                    mb={4}
                  />
                  <Heading size="md" mb={3}>
                    {feature.title}
                  </Heading>
                  <Text color="gray.600" fontSize="sm">
                    {feature.description}
                  </Text>
                </CardBody>
              </MotionCard>
            ))}
          </SimpleGrid>
        </VStack>
      </Container>

      {/* Pricing Section */}
      <Container maxW="7xl" py={16}>
        <VStack spacing={12}>
          <MotionBox
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            textAlign="center"
          >
            <Heading size="xl" mb={4}>
              خطط مرنة لكل احتياجاتك
            </Heading>
            <Text fontSize="lg" color="gray.600" maxW="2xl" mx="auto">
              اختر الخطة المناسبة لك مع إمكانية الترقية في أي وقت
            </Text>
          </MotionBox>

          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={8} w="full">
            {plans.map((plan, index) => (
              <MotionCard
                key={plan.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                position="relative"
                border={plan.popular ? '2px solid' : '1px solid'}
                borderColor={plan.popular ? 'purple.500' : 'gray.200'}
              >
                {plan.popular && (
                  <Badge
                    position="absolute"
                    top="-10px"
                    left="50%"
                    transform="translateX(-50%)"
                    colorScheme="purple"
                    px={3}
                    py={1}
                    borderRadius="full"
                  >
                    الأكثر شعبية
                  </Badge>
                )}
                <CardBody p={6}>
                  <VStack spacing={4} align="stretch">
                    <Box textAlign="center">
                      <Heading size="lg" mb={2}>
                        {plan.name}
                      </Heading>
                      <Text fontSize="2xl" fontWeight="bold" color={`${plan.color}.500`}>
                        {plan.price}
                      </Text>
                      <Text fontSize="sm" color="gray.600">
                        {plan.description}
                      </Text>
                    </Box>
                    
                    <VStack spacing={2} align="stretch">
                      {plan.features.map((feature) => (
                        <Flex key={feature} align="center">
                          <Icon as={FiStar} color="green.500" mr={2} />
                          <Text fontSize="sm">{feature}</Text>
                        </Flex>
                      ))}
                    </VStack>

                    <Button
                      colorScheme={plan.color}
                      variant={plan.popular ? 'solid' : 'outline'}
                      size="lg"
                      w="full"
                      _hover={{ transform: 'translateY(-2px)' }}
                    >
                      {plan.name === 'Free' ? 'ابدأ مجاناً' : 'اختر هذه الخطة'}
                    </Button>
                  </VStack>
                </CardBody>
              </MotionCard>
            ))}
          </SimpleGrid>
        </VStack>
      </Container>

      {/* CTA Section */}
      <Container maxW="7xl" py={16}>
        <MotionBox
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          textAlign="center"
          bg={useColorModeValue('white', 'gray.800')}
          p={12}
          borderRadius="2xl"
          boxShadow="xl"
        >
          <Heading size="xl" mb={4}>
            جاهز لتحويل استراتيجيتك؟
          </Heading>
          <Text fontSize="lg" color="gray.600" mb={8} maxW="2xl" mx="auto">
            انضم إلى آلاف منشئي المحتوى الذين يثقون في ContentSphere
            لإدارة وتنمية حضورهم الرقمي
          </Text>
          <HStack spacing={4} justify="center">
            <Button
              as={Link}
              href="/auth/register"
              size="lg"
              colorScheme="purple"
              rightIcon={<Icon as={FiArrowRight} />}
              _hover={{ transform: 'translateY(-2px)' }}
            >
              ابدأ فترتك التجريبية المجانية
            </Button>
            <Button
              as={Link}
              href="/contact"
              size="lg"
              variant="outline"
              _hover={{ transform: 'translateY(-2px)' }}
            >
              تواصل معنا
            </Button>
          </HStack>
        </MotionBox>
      </Container>
    </Box>
  )
}
